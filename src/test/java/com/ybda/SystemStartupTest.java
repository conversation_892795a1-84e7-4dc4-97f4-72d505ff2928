package com.ybda;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 系统启动测试
 * 验证Spring Boot应用能否正常启动，所有Bean能否正确注入
 */
@SpringBootTest
@ActiveProfiles("test")
public class SystemStartupTest {
    
    /**
     * 测试Spring Boot应用上下文能否正常加载
     */
    @Test
    public void contextLoads() {
        // 如果Spring Boot应用能正常启动，这个测试就会通过
        // 这会验证：
        // 1. 所有@Component、@Service、@Repository注解的类能否正确创建
        // 2. 所有依赖注入是否正确
        // 3. 数据库连接配置是否正确
        // 4. MyBatis Mapper是否能正确扫描和注册
        System.out.println("✅ Spring Boot应用上下文加载成功！");
    }
}
