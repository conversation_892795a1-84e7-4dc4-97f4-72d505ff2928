package com.ybda.service;

import com.ybda.mapper.InspectionRecordMapper;
import com.ybda.mapper.PendingAssetCheckMapper;
import com.ybda.mapper.TrafficAssetMapper;
import com.ybda.model.entity.DetectionRecord;
import com.ybda.model.entity.InspectionRecord;
import com.ybda.model.entity.PendingAssetCheck;
import com.ybda.model.entity.TrafficAsset;
import com.ybda.service.impl.AssetInspectionServiceImpl;
import com.ybda.service.impl.AssetIntegrityServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 测试重复检查记录的防止机制
 */
@ExtendWith(MockitoExtension.class)
public class DuplicateCheckPreventionTest {

    @Mock
    private InspectionRecordMapper inspectionRecordMapper;
    
    @Mock
    private PendingAssetCheckMapper pendingAssetCheckMapper;
    
    @Mock
    private TrafficAssetMapper trafficAssetMapper;

    @InjectMocks
    private AssetInspectionServiceImpl assetInspectionService;
    
    @InjectMocks
    private AssetIntegrityServiceImpl assetIntegrityService;

    @Test
    public void testPreventDuplicateInspectionRecords() {
        // 准备测试数据
        TrafficAsset asset = new TrafficAsset();
        asset.setAssetId("TEST_ASSET_001");
        asset.setType("overhead_sign");
        asset.setName("限速60");
        
        DetectionRecord detectionRecord = new DetectionRecord();
        detectionRecord.setId(1L);
        detectionRecord.setDeviceId("DEVICE_001");
        detectionRecord.setGpsLatitude(28.715456);
        detectionRecord.setGpsLongitude(104.599624);
        detectionRecord.setCreatedTime(LocalDateTime.now());
        
        // 模拟已存在近期的巡检记录
        InspectionRecord existingInspection = new InspectionRecord();
        existingInspection.setAssetId("TEST_ASSET_001");
        existingInspection.setInspectionType("EXISTENCE");
        existingInspection.setCreatedTime(LocalDateTime.now().minusMinutes(1)); // 1分钟前
        
        when(inspectionRecordMapper.selectList(any())).thenReturn(List.of(existingInspection));
        
        // 执行测试
        assetInspectionService.createExistenceInspection(asset, detectionRecord, true);
        
        // 验证：不应该插入新的巡检记录
        verify(inspectionRecordMapper, never()).insert(any(InspectionRecord.class));
    }

    @Test
    public void testPreventDuplicatePendingChecks() {
        // 准备测试数据
        String deviceId = "DEVICE_001";
        String assetId = "TEST_ASSET_001";
        
        // 模拟已存在近期的检查记录
        PendingAssetCheck existingCheck = new PendingAssetCheck();
        existingCheck.setDeviceId(deviceId);
        existingCheck.setExpectedAssetId(assetId);
        existingCheck.setStatus("等待");
        existingCheck.setCreatedTime(LocalDateTime.now().minusMinutes(1)); // 1分钟前
        
        when(pendingAssetCheckMapper.selectList(any())).thenReturn(List.of(existingCheck));
        
        // 执行测试
        assetIntegrityService.createExistenceCheck(deviceId, assetId, "overhead_sign", 
            "限速60", 28.715456, 104.599624, "frame_001");
        
        // 验证：不应该插入新的检查记录
        verify(pendingAssetCheckMapper, never()).insert(any(PendingAssetCheck.class));
    }

    @Test
    public void testAllowCreationWhenNoRecentRecords() {
        // 准备测试数据
        TrafficAsset asset = new TrafficAsset();
        asset.setAssetId("TEST_ASSET_002");
        asset.setType("overhead_sign");
        asset.setName("限速60");
        
        DetectionRecord detectionRecord = new DetectionRecord();
        detectionRecord.setId(2L);
        detectionRecord.setDeviceId("DEVICE_001");
        detectionRecord.setGpsLatitude(28.715456);
        detectionRecord.setGpsLongitude(104.599624);
        detectionRecord.setCreatedTime(LocalDateTime.now());
        
        // 模拟没有近期的巡检记录
        when(inspectionRecordMapper.selectList(any())).thenReturn(List.of());
        when(inspectionRecordMapper.getNextInspectionNumberByAsset(any())).thenReturn(1);
        when(inspectionRecordMapper.insert(any())).thenReturn(1);
        
        // 执行测试
        assetInspectionService.createExistenceInspection(asset, detectionRecord, true);
        
        // 验证：应该插入新的巡检记录
        verify(inspectionRecordMapper, times(1)).insert(any(InspectionRecord.class));
    }
}
