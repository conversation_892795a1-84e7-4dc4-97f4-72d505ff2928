package com.ybda.service;

import com.ybda.mapper.DetectionDetailMapper;
import com.ybda.mapper.DetectionRecordMapper;
import com.ybda.mapper.PendingAssetCheckMapper;
import com.ybda.mapper.TrafficAssetMapper;
import com.ybda.model.dto.DetectionRequestDTO;
import com.ybda.model.dto.SignDetectionDTO;
import com.ybda.model.entity.DetectionRecord;
import com.ybda.model.entity.PendingAssetCheck;
import com.ybda.model.entity.TrafficAsset;
import com.ybda.service.impl.DetectionServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 测试新增资产时创建已确认检查记录的功能
 */
@ExtendWith(MockitoExtension.class)
public class DetectionServiceNewAssetTest {

    @Mock
    private DetectionRecordMapper detectionRecordMapper;
    
    @Mock
    private DetectionDetailMapper detectionDetailMapper;
    
    @Mock
    private PendingAssetCheckMapper pendingAssetCheckMapper;
    
    @Mock
    private TrafficAssetMapper trafficAssetMapper;
    
    @Mock
    private LocationCacheService locationCacheService;
    
    @Mock
    private SignGroundMatchingService signGroundMatchingService;
    
    @Mock
    private AssetIntegrityService assetIntegrityService;

    @InjectMocks
    private DetectionServiceImpl detectionService;

    @Test
    public void testNewAssetCreatesConfirmedCheck() {
        // 准备测试数据
        DetectionRequestDTO request = new DetectionRequestDTO();
        request.setFrameId("TEST_FRAME_001");
        request.setTimestamp(System.currentTimeMillis());
        request.setImageUrl("/test/image.jpg");
        
        SignDetectionDTO sign = new SignDetectionDTO();
        sign.setType("overhead_sign");
        sign.setName("限速60");
        sign.setClassId(1);
        sign.setBbox(List.of(100.0, 50.0, 200.0, 150.0));
        
        request.setSigns(List.of(sign));
        
        // Mock GPS位置
        when(locationCacheService.getCurrentLocation()).thenReturn(
            new com.ybda.model.entity.DeviceLocation("DEVICE_001", 39.9042, 116.4074, LocalDateTime.now())
        );
        
        // Mock 数据库操作
        when(detectionRecordMapper.insert(any(DetectionRecord.class))).thenReturn(1);
        when(detectionDetailMapper.batchInsert(any())).thenReturn(1);
        when(trafficAssetMapper.findNearbyAssets(any(), any(), any())).thenReturn(List.of()); // 没有附近资产
        when(trafficAssetMapper.insert(any(TrafficAsset.class))).thenReturn(1);
        when(detectionDetailMapper.updateTrafficAssetId(any(), any())).thenReturn(1);
        when(pendingAssetCheckMapper.insert(any(PendingAssetCheck.class))).thenReturn(1);
        when(detectionRecordMapper.updateProcessStatus(any(), any())).thenReturn(1);
        
        // 执行测试
        detectionService.processDetectionRequest(request);
        
        // 验证结果
        verify(trafficAssetMapper, times(1)).insert(any(TrafficAsset.class));
        verify(pendingAssetCheckMapper, times(1)).insert(any(PendingAssetCheck.class));
        
        // 验证创建的检查记录状态为"已确认"
        verify(pendingAssetCheckMapper).insert(argThat(check -> 
            "已确认".equals(check.getStatus()) && 
            "存在性".equals(check.getCheckType())
        ));
    }
}
