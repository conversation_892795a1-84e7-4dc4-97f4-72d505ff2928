<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybda.mapper.TrafficAssetMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.ybda.model.entity.TrafficAsset">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="asset_id" property="assetId" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="geometry_type" property="geometryType" jdbcType="VARCHAR"/>
        <result column="latitude" property="latitude" jdbcType="DECIMAL"/>
        <result column="longitude" property="longitude" jdbcType="DECIMAL"/>
        <result column="geometry_coordinates" property="geometryCoordinates" jdbcType="LONGVARCHAR"/>
        <result column="first_detected_time" property="firstDetectedTime" jdbcType="TIMESTAMP"/>
        <result column="last_detected_time" property="lastDetectedTime" jdbcType="TIMESTAMP"/>
        <result column="detection_count" property="detectionCount" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="available" property="available" jdbcType="INTEGER"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, asset_id, type, name, geometry_type, latitude, longitude, geometry_coordinates,
        first_detected_time, last_detected_time, detection_count, status, available, created_time, updated_time
    </sql>

    <!-- 根据资产ID查询 -->
    <select id="selectByAssetId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM traffic_assets
        WHERE asset_id = #{assetId}
    </select>

    <!-- 查找附近的资产 -->
    <select id="findNearbyAssets" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM traffic_assets
        WHERE (
            6371000 * ACOS(
                COS(RADIANS(#{latitude})) * COS(RADIANS(latitude)) * 
                COS(RADIANS(longitude) - RADIANS(#{longitude})) + 
                SIN(RADIANS(#{latitude})) * SIN(RADIANS(latitude))
            )
        ) &lt;= #{distance}
        ORDER BY (
            6371000 * ACOS(
                COS(RADIANS(#{latitude})) * COS(RADIANS(latitude)) * 
                COS(RADIANS(longitude) - RADIANS(#{longitude})) + 
                SIN(RADIANS(#{latitude})) * SIN(RADIANS(latitude))
            )
        )
    </select>

    <!-- 根据地理范围查询资产 -->
    <select id="selectByBounds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM traffic_assets
        WHERE latitude BETWEEN #{minLat} AND #{maxLat}
        AND longitude BETWEEN #{minLng} AND #{maxLng}
        ORDER BY created_time DESC
    </select>

    <!-- 更新资产状态 -->
    <update id="updateStatus">
        UPDATE traffic_assets
        SET status = #{status}, updated_time = NOW()
        WHERE asset_id = #{assetId}
    </update>

    <!-- 更新资产检测信息 -->
    <update id="updateDetectionInfo">
        UPDATE traffic_assets
        SET last_detected_time = #{lastDetectedTime},
            detection_count = #{detectionCount},
            updated_time = NOW()
        WHERE asset_id = #{assetId}
    </update>

    <!-- 统计各状态的资产数量 -->
    <select id="countByStatus" resultType="java.util.Map">
        SELECT status, COUNT(*) as count
        FROM traffic_assets
        GROUP BY status
    </select>

    <!-- 统计各类型的资产数量 -->
    <select id="countByType" resultType="java.util.Map">
        SELECT type, COUNT(*) as count
        FROM traffic_assets
        GROUP BY type
    </select>

    <!-- 查询缺失的资产 -->
    <select id="selectMissingAssets" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM traffic_assets
        WHERE status = '缺失'
        ORDER BY last_detected_time DESC
    </select>

    <!-- 查找指定位置附近的点状资产 -->
    <select id="findNearbyPointAssets" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM traffic_assets
        WHERE geometry_type = 'POINT'
          AND latitude IS NOT NULL
          AND longitude IS NOT NULL
          AND (
            6371000 * acos(
              cos(radians(#{latitude})) * cos(radians(latitude)) *
              cos(radians(longitude) - radians(#{longitude})) +
              sin(radians(#{latitude})) * sin(radians(latitude))
            )
          ) &lt;= #{distance}
        ORDER BY (
          6371000 * acos(
            cos(radians(#{latitude})) * cos(radians(latitude)) *
            cos(radians(longitude) - radians(#{longitude})) +
            sin(radians(#{latitude})) * sin(radians(latitude))
          )
        )
    </select>

    <!-- 查找所有线状和面状资产 -->
    <select id="findComplexGeometryAssets" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM traffic_assets
        WHERE geometry_type IN ('LINE', 'POLYGON')
          AND geometry_coordinates IS NOT NULL
        ORDER BY type, asset_id
    </select>

    <!-- 更新资产的几何坐标数据 -->
    <update id="updateGeometryCoordinates">
        UPDATE traffic_assets
        SET geometry_coordinates = #{geometryCoordinates},
            updated_time = NOW()
        WHERE asset_id = #{assetId}
    </update>

</mapper>
