<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybda.mapper.DetectionRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.ybda.model.entity.DetectionRecord">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="frame_id" property="frameId" jdbcType="VARCHAR"/>
        <result column="timestamp" property="timestamp" jdbcType="BIGINT"/>
        <result column="gps_latitude" property="gpsLatitude" jdbcType="DECIMAL"/>
        <result column="gps_longitude" property="gpsLongitude" jdbcType="DECIMAL"/>
        <result column="device_id" property="deviceId" jdbcType="VARCHAR"/>
        <result column="image_url" property="imageUrl" jdbcType="VARCHAR"/>
        <result column="process_status" property="processStatus" jdbcType="VARCHAR"/>
        <result column="raw_data" property="rawData" jdbcType="LONGVARCHAR"/>
        <result column="related_asset_ids" property="relatedAssetIds" jdbcType="LONGVARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, frame_id, timestamp, gps_latitude, gps_longitude, device_id,
        image_url, process_status, raw_data, related_asset_ids, created_time
    </sql>

    <!-- 根据帧ID查询检测记录 -->
    <select id="selectByFrameId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM detection_records
        WHERE frame_id = #{frameId}
    </select>

    <!-- 根据处理状态查询检测记录 -->
    <select id="selectByProcessStatus" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM detection_records
        WHERE process_status = #{processStatus}
        ORDER BY created_time DESC
    </select>

    <!-- 根据设备ID查询检测记录 -->
    <select id="selectByDeviceId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM detection_records
        WHERE device_id = #{deviceId}
        ORDER BY created_time DESC
    </select>

    <!-- 根据时间范围查询检测记录 -->
    <select id="selectByTimeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM detection_records
        WHERE created_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY created_time ASC
    </select>

    <!-- 查询指定位置和时间范围内的检测记录 -->
    <select id="findNearbyRecords" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM detection_records
        WHERE gps_latitude IS NOT NULL 
        AND gps_longitude IS NOT NULL
        AND (
            6371000 * ACOS(
                COS(RADIANS(#{latitude})) * COS(RADIANS(gps_latitude)) * 
                COS(RADIANS(gps_longitude) - RADIANS(#{longitude})) + 
                SIN(RADIANS(#{latitude})) * SIN(RADIANS(gps_latitude))
            )
        ) &lt;= #{distance}
        AND created_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY created_time ASC
    </select>

    <!-- 更新处理状态 -->
    <update id="updateProcessStatus">
        UPDATE detection_records
        SET process_status = #{processStatus}
        WHERE id = #{id}
    </update>

    <!-- 批量更新处理状态 -->
    <update id="batchUpdateProcessStatus">
        UPDATE detection_records
        SET process_status = #{processStatus}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 统计各处理状态的记录数量 -->
    <select id="countByProcessStatus" resultType="java.util.Map">
        SELECT process_status, COUNT(*) as count
        FROM detection_records
        GROUP BY process_status
    </select>

    <!-- 统计总记录数 -->
    <select id="countTotal" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM detection_records
    </select>

    <!-- 统计待处理记录数 -->
    <select id="countPending" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM detection_records WHERE process_status = '待确认'
    </select>

    <!-- 统计处理失败记录数 -->
    <select id="countFailed" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM detection_records WHERE process_status = '失败'
    </select>

    <!-- 分页查询检测记录 -->
    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM detection_records
        <where>
            <if test="deviceId != null and deviceId != ''">
                AND device_id = #{deviceId}
            </if>
            <if test="processStatus != null and processStatus != ''">
                AND process_status = #{processStatus}
            </if>
        </where>
        ORDER BY created_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 删除指定时间之前的检测记录 -->
    <delete id="deleteBeforeTime">
        DELETE FROM detection_records
        WHERE created_time &lt; #{beforeTime}
    </delete>

    <!-- 查询最新的检测记录 -->
    <select id="selectLatest" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM detection_records
        ORDER BY created_time DESC
        LIMIT #{limit}
    </select>

    <!-- 更新检测记录的资产关联信息 -->
    <update id="updateAssetRelation">
        UPDATE detection_records
        SET related_asset_ids = #{relatedAssetIds}
        WHERE id = #{id}
    </update>

</mapper>
