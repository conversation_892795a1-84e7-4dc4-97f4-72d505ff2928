<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybda.mapper.AlertRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.ybda.model.entity.AlertRecord">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="alert_type" property="alertType" jdbcType="VARCHAR"/>
        <result column="asset_id" property="assetId" jdbcType="VARCHAR"/>
        <result column="alert_message" property="alertMessage" jdbcType="LONGVARCHAR"/>
        <result column="alert_details" property="alertDetails" jdbcType="LONGVARCHAR"/>
        <result column="alert_time" property="alertTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="resolved_time" property="resolvedTime" jdbcType="TIMESTAMP"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, alert_type, asset_id, alert_message, alert_details,
        alert_time, status, resolved_time, created_time
    </sql>

    <!-- 根据报警类型查询 -->
    <select id="selectByAlertType" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_records
        WHERE alert_type = #{alertType}
        ORDER BY alert_time DESC
    </select>

    <!-- 根据状态查询报警记录 -->
    <select id="selectByStatus" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_records
        WHERE status = #{status}
        ORDER BY alert_time DESC
    </select>

    <!-- 根据资产ID查询报警记录 -->
    <select id="selectByAssetId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_records
        WHERE asset_id = #{assetId}
        ORDER BY alert_time DESC
    </select>

    <!-- 根据时间范围查询报警记录 -->
    <select id="selectByTimeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_records
        WHERE alert_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY alert_time DESC
    </select>

    <!-- 查询活跃的报警记录 -->
    <select id="selectActiveAlerts" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_records
        WHERE status = '活跃'
        ORDER BY alert_time DESC
    </select>

    <!-- 更新报警状态 -->
    <update id="updateStatus">
        UPDATE alert_records
        SET status = #{status},
            resolved_time = #{resolvedTime}
        WHERE id = #{id}
    </update>

    <!-- 批量更新报警状态 -->
    <update id="batchUpdateStatus">
        UPDATE alert_records
        SET status = #{status},
            resolved_time = #{resolvedTime}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 统计各类型的报警数量 -->
    <select id="countByAlertType" resultType="java.util.Map">
        SELECT alert_type, COUNT(*) as count
        FROM alert_records
        GROUP BY alert_type
    </select>

    <!-- 统计各状态的报警数量 -->
    <select id="countByStatus" resultType="java.util.Map">
        SELECT status, COUNT(*) as count
        FROM alert_records
        GROUP BY status
    </select>

    <!-- 统计总报警数 -->
    <select id="countTotal" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM alert_records
    </select>

    <!-- 统计活跃报警数 -->
    <select id="countActive" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM alert_records WHERE status = '活跃'
    </select>

    <!-- 分页查询报警记录 -->
    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_records
        <where>
            <if test="alertType != null and alertType != ''">
                AND alert_type = #{alertType}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
        ORDER BY alert_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 删除指定时间之前的报警记录 -->
    <delete id="deleteBeforeTime">
        DELETE FROM alert_records
        WHERE created_time &lt; #{beforeTime}
    </delete>

    <!-- 查询最新的报警记录 -->
    <select id="selectLatest" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM alert_records
        ORDER BY alert_time DESC
        LIMIT #{limit}
    </select>

    <!-- 检查是否存在相同的活跃报警 -->
    <select id="countSimilarActiveAlerts" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM alert_records
        WHERE alert_type = #{alertType}
        AND status = '活跃'
        <if test="assetId != null">
            AND asset_id = #{assetId}
        </if>
        AND alert_time >= DATE_SUB(NOW(), INTERVAL #{recentHours} HOUR)
    </select>

</mapper>
