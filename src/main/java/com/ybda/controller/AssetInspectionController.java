package com.ybda.controller;

import com.ybda.service.AssetInspectionService;
import com.ybda.util.DatabaseMigrationUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 资产巡检控制器
 * 提供资产巡检相关的API接口
 */
@Slf4j
@RestController
@RequestMapping("/api/asset-inspection")
@RequiredArgsConstructor
public class AssetInspectionController {

    private final AssetInspectionService assetInspectionService;
    private final DatabaseMigrationUtil migrationUtil;

    /**
     * 获取资产巡检统计信息
     */
    @GetMapping("/stats/{assetId}")
    public ResponseEntity<Map<String, Object>> getAssetInspectionStats(
            @PathVariable String assetId,
            @RequestParam(defaultValue = "10") int recentCount) {
        
        try {
            AssetInspectionService.AssetInspectionStats stats = 
                assetInspectionService.getAssetInspectionStats(assetId, recentCount);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", stats);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("获取资产巡检统计失败: assetId={}", assetId, e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取巡检统计失败: " + e.getMessage());
            
            return ResponseEntity.status(500).body(result);
        }
    }

    /**
     * 分析资产存在概率
     */
    @GetMapping("/probability/{assetId}")
    public ResponseEntity<Map<String, Object>> analyzeAssetExistenceProbability(
            @PathVariable String assetId,
            @RequestParam(defaultValue = "10") int recentCount) {
        
        try {
            double probability = assetInspectionService.analyzeAssetExistenceProbability(assetId, recentCount);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", Map.of(
                "assetId", assetId,
                "probability", probability,
                "recentCount", recentCount,
                "status", getStatusByProbability(probability)
            ));
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("分析资产存在概率失败: assetId={}", assetId, e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "分析失败: " + e.getMessage());
            
            return ResponseEntity.status(500).body(result);
        }
    }

    /**
     * 手动更新资产状态
     */
    @PostMapping("/update-status/{assetId}")
    public ResponseEntity<Map<String, Object>> updateAssetStatus(@PathVariable String assetId) {
        
        try {
            assetInspectionService.updateAssetStatusBasedOnInspections(assetId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "资产状态更新成功");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("更新资产状态失败: assetId={}", assetId, e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "更新失败: " + e.getMessage());
            
            return ResponseEntity.status(500).body(result);
        }
    }

    /**
     * 手动清理旧巡检记录
     */
    @PostMapping("/cleanup")
    public ResponseEntity<Map<String, Object>> cleanupOldRecords(
            @RequestParam(defaultValue = "50") int keepCount) {
        
        try {
            assetInspectionService.cleanupOldInspectionRecords(keepCount);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "清理完成，每个资产保留最新" + keepCount + "条记录");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("清理巡检记录失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "清理失败: " + e.getMessage());
            
            return ResponseEntity.status(500).body(result);
        }
    }

    /**
     * 获取巡检系统概览
     */
    @GetMapping("/overview")
    public ResponseEntity<Map<String, Object>> getInspectionOverview() {
        
        try {
            // 这里可以添加系统级别的统计信息
            Map<String, Object> overview = new HashMap<>();
            overview.put("description", "资产巡检系统");
            overview.put("features", new String[]{
                "自动记录资产存在性巡检",
                "基于巡检历史分析资产状态",
                "支持10次巡检的概率统计",
                "自动更新资产状态（正常/疑似缺失/缺失）"
            });
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", overview);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("获取巡检概览失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取概览失败: " + e.getMessage());
            
            return ResponseEntity.status(500).body(result);
        }
    }

    /**
     * 手动修复数据库结构
     */
    @PostMapping("/fix-database")
    public ResponseEntity<Map<String, Object>> fixDatabaseStructure() {

        try {
            log.info("🔧 手动触发数据库结构修复");

            // 检查表结构
            boolean structureOk = migrationUtil.checkTableStructure();

            if (!structureOk) {
                // 执行修复
                migrationUtil.fixInspectionRecordsConstraints();

                Map<String, Object> result = new HashMap<>();
                result.put("success", true);
                result.put("message", "数据库结构修复完成");

                return ResponseEntity.ok(result);
            } else {
                Map<String, Object> result = new HashMap<>();
                result.put("success", true);
                result.put("message", "数据库结构正常，无需修复");

                return ResponseEntity.ok(result);
            }

        } catch (Exception e) {
            log.error("数据库结构修复失败", e);

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "修复失败: " + e.getMessage());

            return ResponseEntity.status(500).body(result);
        }
    }

    /**
     * 检查数据库结构
     */
    @GetMapping("/check-database")
    public ResponseEntity<Map<String, Object>> checkDatabaseStructure() {

        try {
            boolean structureOk = migrationUtil.checkTableStructure();

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", Map.of(
                "structureOk", structureOk,
                "message", structureOk ? "数据库结构正常" : "数据库结构需要修复"
            ));

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("检查数据库结构失败", e);

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "检查失败: " + e.getMessage());

            return ResponseEntity.status(500).body(result);
        }
    }

    /**
     * 根据概率获取状态描述
     */
    private String getStatusByProbability(double probability) {
        if (probability >= 0.8) {
            return "正常";
        } else if (probability >= 0.2) {
            return "疑似缺失";
        } else {
            return "缺失";
        }
    }
}
