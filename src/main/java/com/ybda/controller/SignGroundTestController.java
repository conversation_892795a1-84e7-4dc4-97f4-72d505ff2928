package com.ybda.controller;

import com.ybda.mapper.InspectionRecordMapper;
import com.ybda.model.entity.InspectionRecord;
import com.ybda.config.SignVerificationConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 标牌地面对应关系测试控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/sign-ground")
@RequiredArgsConstructor
public class SignGroundTestController {

    /**
     * 获取完整的验证流程示例
     */
    @GetMapping("/verification-flow-example")
    public ResponseEntity<Map<String, Object>> getVerificationFlowExample() {

        Map<String, Object> result = new HashMap<>();

        try {
            Map<String, Object> flowExample = new HashMap<>();

            // 正常流程
            flowExample.put("正常验证流程", List.of(
                "1. 检测到空中标牌: '1左2直3右'",
                "2. 创建验证任务，期望3个车道方向",
                "3. 检测到地面标线: '左转箭头' → 匹配成功 (1/3)",
                "4. 检测到地面标线: '直行箭头' → 匹配成功 (2/3)",
                "5. 检测到地面标线: '右转箭头' → 匹配成功 (3/3)",
                "6. 检测到路口结束标识: '斑马线' → 验证完成 ✅"
            ));

            // 不匹配流程
            flowExample.put("不匹配报警流程", List.of(
                "1. 检测到空中标牌: '1左2直3右'",
                "2. 创建验证任务，期望3个车道方向",
                "3. 检测到地面标线: '左转箭头' → 匹配成功 (1/3)",
                "4. 检测到地面标线: '直行箭头' → 匹配成功 (2/3)",
                "5. 检测到路口结束标识: '停止线' → 缺少右转箭头 → 触发报警 🚨"
            ));

            // 兜底超时流程
            flowExample.put("兜底超时流程", List.of(
                "1. 检测到空中标牌: '1左2直3右'",
                "2. 创建验证任务，期望3个车道方向",
                "3. 检测到部分地面标线...",
                "4. 10分钟内未检测到路口结束标识",
                "5. 兜底超时机制触发 → 报警 🚨"
            ));

            result.put("success", true);
            result.put("verificationFlows", flowExample);
            result.put("endMarkings", List.of("斑马线", "人行横道", "停止线", "停车线"));
            result.put("timeoutMinutes", 10);
            result.put("message", "标牌验证以路口结束标识为准，兜底超时2分钟");

        } catch (Exception e) {
            log.error("获取验证流程示例失败", e);
            result.put("success", false);
            result.put("message", "获取失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 获取所有支持的标牌类型和验证规则
     */
    @GetMapping("/supported-sign-types")
    public ResponseEntity<Map<String, Object>> getSupportedSignTypes() {

        Map<String, Object> result = new HashMap<>();

        try {
            Map<String, Object> signTypes = new HashMap<>();

            // 遍历所有验证规则
            SignVerificationConfig.VERIFICATION_RULES.forEach((ruleType, rule) -> {
                Map<String, Object> ruleInfo = new HashMap<>();
                ruleInfo.put("signKeywords", rule.getSignKeywords());
                ruleInfo.put("expectedMarkings", rule.getExpectedMarkings());
                ruleInfo.put("description", rule.getDescription());
                signTypes.put(ruleType, ruleInfo);
            });

            // 添加示例
            Map<String, Object> examples = new HashMap<>();
            examples.put("车道指示", List.of("1左2直3右", "第1车道左转第2车道直行", "左转直行右转"));
            examples.put("限速标牌", List.of("限速60", "限速40公里", "Speed Limit 80"));
            examples.put("停车标牌", List.of("禁止停车", "停车场", "临时停车"));
            examples.put("禁止标牌", List.of("禁止左转", "禁止通行", "严禁鸣笛"));
            examples.put("学校区域", List.of("学校区域减速慢行", "前方学校", "注意儿童"));
            examples.put("施工标牌", List.of("前方施工", "道路维修", "施工车辆出入"));
            examples.put("指路标牌", List.of("机场出口", "市区方向", "服务区入口"));

            result.put("success", true);
            result.put("verificationRules", signTypes);
            result.put("examples", examples);
            result.put("totalRuleTypes", SignVerificationConfig.VERIFICATION_RULES.size());
            result.put("message", "系统支持多种类型的标牌验证，不仅限于箭头和车道指示");

            log.info("🧪 查询支持的标牌类型: {}种规则类型", SignVerificationConfig.VERIFICATION_RULES.size());

        } catch (Exception e) {
            log.error("查询支持的标牌类型失败", e);
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }
}
