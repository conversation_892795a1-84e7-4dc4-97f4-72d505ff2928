package com.ybda.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ybda.model.entity.AlertRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 报警记录Mapper接口
 */
@Mapper
public interface AlertRecordMapper extends BaseMapper<AlertRecord> {
    
    /**
     * 根据报警类型查询
     */
    List<AlertRecord> selectByAlertType(@Param("alertType") String alertType);
    
    /**
     * 根据状态查询报警记录
     */
    List<AlertRecord> selectByStatus(@Param("status") String status);
    
    /**
     * 根据资产ID查询报警记录
     */
    List<AlertRecord> selectByAssetId(@Param("assetId") String assetId);
    
    /**
     * 根据时间范围查询报警记录
     */
    List<AlertRecord> selectByTimeRange(@Param("startTime") LocalDateTime startTime,
                                       @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查询活跃的报警记录
     */
    List<AlertRecord> selectActiveAlerts();
    
    /**
     * 更新报警状态
     */
    int updateStatus(@Param("id") Long id, 
                    @Param("status") String status,
                    @Param("resolvedTime") LocalDateTime resolvedTime);
    
    /**
     * 批量更新报警状态
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids,
                         @Param("status") String status,
                         @Param("resolvedTime") LocalDateTime resolvedTime);
    
    /**
     * 统计各类型的报警数量
     */
    List<java.util.Map<String, Object>> countByAlertType();
    
    /**
     * 统计各状态的报警数量
     */
    List<java.util.Map<String, Object>> countByStatus();
    
    /**
     * 统计总报警数
     */
    int countTotal();
    
    /**
     * 统计活跃报警数
     */
    int countActive();
    
    /**
     * 分页查询报警记录
     */
    List<AlertRecord> selectByPage(@Param("offset") Integer offset,
                                  @Param("limit") Integer limit,
                                  @Param("alertType") String alertType,
                                  @Param("status") String status);
    
    /**
     * 删除指定时间之前的报警记录（数据清理）
     */
    int deleteBeforeTime(@Param("beforeTime") LocalDateTime beforeTime);
    
    /**
     * 查询最新的报警记录
     */
    List<AlertRecord> selectLatest(@Param("limit") Integer limit);
    
    /**
     * 检查是否存在相同的活跃报警（防重复报警）
     */
    int countSimilarActiveAlerts(@Param("alertType") String alertType,
                                @Param("assetId") String assetId,
                                @Param("recentHours") Integer recentHours);
}
