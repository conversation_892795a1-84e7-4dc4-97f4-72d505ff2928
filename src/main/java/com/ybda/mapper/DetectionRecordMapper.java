package com.ybda.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ybda.model.entity.DetectionRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 检测记录Mapper接口
 */
@Mapper
public interface DetectionRecordMapper extends BaseMapper<DetectionRecord> {

    /**
     * 根据帧ID查询检测记录
     */
    DetectionRecord selectByFrameId(@Param("frameId") String frameId);

    /**
     * 根据处理状态查询检测记录
     */
    List<DetectionRecord> selectByProcessStatus(@Param("processStatus") String processStatus);

    /**
     * 根据设备ID查询检测记录
     */
    List<DetectionRecord> selectByDeviceId(@Param("deviceId") String deviceId);
    
    /**
     * 根据时间范围查询检测记录
     */
    List<DetectionRecord> selectByTimeRange(@Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查询指定位置和时间范围内的检测记录
     * @param latitude 纬度
     * @param longitude 经度
     * @param distance 距离(米)
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 匹配的检测记录列表
     */
    List<DetectionRecord> findNearbyRecords(@Param("latitude") Double latitude,
                                           @Param("longitude") Double longitude,
                                           @Param("distance") Double distance,
                                           @Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);
    
    /**
     * 更新处理状态
     */
    int updateProcessStatus(@Param("id") Long id, @Param("processStatus") String processStatus);
    
    /**
     * 批量更新处理状态
     */
    int batchUpdateProcessStatus(@Param("ids") List<Long> ids, @Param("processStatus") String processStatus);
    
    /**
     * 统计各处理状态的记录数量
     */
    List<java.util.Map<String, Object>> countByProcessStatus();
    
    /**
     * 统计总记录数
     */
    int countTotal();
    
    /**
     * 统计待处理记录数
     */
    int countPending();
    
    /**
     * 统计处理失败记录数
     */
    int countFailed();
    
    /**
     * 分页查询检测记录
     */
    List<DetectionRecord> selectByPage(@Param("offset") Integer offset,
                                      @Param("limit") Integer limit,
                                      @Param("deviceId") String deviceId,
                                      @Param("processStatus") String processStatus);
    
    /**
     * 删除指定时间之前的检测记录（数据清理）
     */
    int deleteBeforeTime(@Param("beforeTime") LocalDateTime beforeTime);
    
    /**
     * 查询最新的检测记录
     */
    List<DetectionRecord> selectLatest(@Param("limit") Integer limit);

    /**
     * 更新检测记录的资产关联信息
     */
    int updateAssetRelation(@Param("id") Long id,
                           @Param("relatedAssetIds") String relatedAssetIds);
}
