package com.ybda.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ybda.model.entity.DetectionDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 检测详情Mapper接口
 */
@Mapper
public interface DetectionDetailMapper extends BaseMapper<DetectionDetail> {
    
    /**
     * 批量插入检测详情
     */
    int batchInsert(@Param("details") List<DetectionDetail> details);
    
    /**
     * 根据检测记录ID查询详情列表
     */
    List<DetectionDetail> selectByDetectionRecordId(@Param("detectionRecordId") Long detectionRecordId);
    
    /**
     * 根据资产ID查询检测详情
     */
    List<DetectionDetail> selectByTrafficAssetId(@Param("trafficAssetId") Long trafficAssetId);
    
    /**
     * 根据类型查询检测详情
     */
    List<DetectionDetail> selectByType(@Param("type") String type);
    
    /**
     * 根据跟踪ID查询检测详情
     */
    List<DetectionDetail> selectByTrackId(@Param("trackId") Integer trackId);
    
    /**
     * 更新关联的资产ID
     */
    int updateTrafficAssetId(@Param("id") Long id, @Param("trafficAssetId") Long trafficAssetId);
    
    /**
     * 批量更新关联的资产ID
     */
    int batchUpdateTrafficAssetId(@Param("ids") List<Long> ids, @Param("trafficAssetId") Long trafficAssetId);
    
    /**
     * 根据检测记录ID删除详情
     */
    int deleteByDetectionRecordId(@Param("detectionRecordId") Long detectionRecordId);
    
    /**
     * 统计各类型的检测详情数量
     */
    List<java.util.Map<String, Object>> countByType();
    
    /**
     * 统计总详情数
     */
    int countTotal();
    
    /**
     * 查询未关联资产的检测详情
     */
    List<DetectionDetail> selectUnlinkedDetails();
    
    /**
     * 分页查询检测详情
     */
    List<DetectionDetail> selectByPage(@Param("offset") Integer offset,
                                      @Param("limit") Integer limit,
                                      @Param("type") String type,
                                      @Param("trafficAssetId") Long trafficAssetId);
}
