package com.ybda.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ybda.model.entity.PendingAssetCheck;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 待确认资产检查Mapper接口
 */
@Mapper
public interface PendingAssetCheckMapper extends BaseMapper<PendingAssetCheck> {
    
    /**
     * 批量插入待确认资产检查
     */
    int batchInsert(@Param("checks") List<PendingAssetCheck> checks);

    /**
     * 根据状态查询待确认检查
     */
    List<PendingAssetCheck> selectByStatus(@Param("status") String status);
    
    /**
     * 查询超时的待确认检查
     * @param timeoutBefore 超时时间点
     * @return 超时的检查列表
     */
    List<PendingAssetCheck> findTimeoutPendingChecks(@Param("timeoutBefore") LocalDateTime timeoutBefore);
    
    /**
     * 查找指定位置和时间范围内的待确认检查
     * @param latitude 纬度
     * @param longitude 经度
     * @param distance 距离(米)
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 匹配的待确认检查列表
     */
    List<PendingAssetCheck> searchForNearbyRecordsToBeConfirmedByTime(@Param("latitude") Double latitude,
                                                    @Param("longitude") Double longitude,
                                                    @Param("distance") Double distance,
                                                    @Param("startTime") LocalDateTime startTime,
                                                    @Param("endTime") LocalDateTime endTime);

    /**
     * 批量确认检查记录
     */
    int batchConfirmChecks(@Param("ids") List<Long> ids,
                          @Param("detectionRecordId") Long detectionRecordId,
                          @Param("confirmedTime") LocalDateTime confirmedTime);
    

    /**
     * 批量标记为缺失
     */
    int batchMarkAsMissing(@Param("ids") List<Long> ids, @Param("confirmedTime") LocalDateTime confirmedTime);
    
    /**
     * 统计各状态的检查数量
     */
    List<java.util.Map<String, Object>> countByStatus();


    /**
     * 根据检查类型和状态查询超时任务
     */
    List<PendingAssetCheck> findTimeoutChecks(@Param("checkType") String checkType,
                                             @Param("status") String status,
                                             @Param("timeoutTime") LocalDateTime timeoutTime);

    /**
     * 根据设备ID和状态查询
     */
    List<PendingAssetCheck> selectByDeviceIdAndStatus(@Param("deviceId") String deviceId,
                                                     @Param("status") String status);


    /**
     * 查找附近的待检查任务
     */
    List<PendingAssetCheck> findNearbyPendingChecks(@Param("latitude") Double latitude,
                                                   @Param("longitude") Double longitude,
                                                   @Param("distance") Double distance,
                                                   @Param("checkType") String checkType,
                                                   @Param("status") String status);
}
