package com.ybda.util;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

/**
 * 数据库迁移工具类
 * 用于修复 inspection_records 表的外键约束问题
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DatabaseMigrationUtil {

    private final JdbcTemplate jdbcTemplate;

    /**
     * 修复 inspection_records 表的外键约束问题
     */
    public void fixInspectionRecordsConstraints() {
        try {
            log.info("🔧 开始修复 inspection_records 表的外键约束...");

            // 1. 检查并删除外键约束
            try {
                jdbcTemplate.execute("ALTER TABLE inspection_records DROP FOREIGN KEY inspection_records_ibfk_1");
                log.info("✅ 删除外键约束 inspection_records_ibfk_1 成功");
            } catch (Exception e) {
                log.warn("⚠️ 删除外键约束失败（可能已经不存在）: {}", e.getMessage());
            }

            // 2. 修改 task_id 字段允许为空
            try {
                jdbcTemplate.execute("ALTER TABLE inspection_records MODIFY COLUMN task_id VARCHAR(100) COMMENT '关联的验证任务ID（可为空）'");
                log.info("✅ 修改 task_id 字段为可空成功");
            } catch (Exception e) {
                log.warn("⚠️ 修改 task_id 字段失败: {}", e.getMessage());
            }

            // 3. 添加新字段（如果不存在）
            addColumnIfNotExists("asset_id", "VARCHAR(64)", "关联的资产ID（通用巡检使用）");
            addColumnIfNotExists("inspection_type", "VARCHAR(32) DEFAULT 'CORRESPONDENCE'", "巡检类型：EXISTENCE(存在性)/CORRESPONDENCE(对应性)");
            addColumnIfNotExists("detection_result", "TINYINT DEFAULT 0", "检测结果：1=检测到，0=未检测到");
            addColumnIfNotExists("detection_record_id", "BIGINT", "关联的检测记录ID（如果检测到）");
            addColumnIfNotExists("gps_latitude", "DECIMAL(10,8)", "巡检时GPS纬度");
            addColumnIfNotExists("gps_longitude", "DECIMAL(11,8)", "巡检时GPS经度");

            // 4. 添加索引（如果不存在）
            addIndexIfNotExists("idx_asset_id", "asset_id");
            addIndexIfNotExists("idx_inspection_type", "inspection_type");
            addIndexIfNotExists("idx_detection_result", "detection_result");

            // 5. 更新现有记录的 inspection_type
            try {
                int updated = jdbcTemplate.update(
                    "UPDATE inspection_records SET inspection_type = 'CORRESPONDENCE' WHERE inspection_type IS NULL OR inspection_type = ''"
                );
                log.info("✅ 更新现有记录的 inspection_type 成功，影响 {} 条记录", updated);
            } catch (Exception e) {
                log.warn("⚠️ 更新现有记录失败: {}", e.getMessage());
            }

            log.info("🎉 inspection_records 表修复完成！");

        } catch (Exception e) {
            log.error("❌ 修复 inspection_records 表失败", e);
            throw new RuntimeException("数据库修复失败", e);
        }
    }

    /**
     * 添加字段（如果不存在）
     */
    private void addColumnIfNotExists(String columnName, String columnDefinition, String comment) {
        try {
            // 检查字段是否存在
            Integer count = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'inspection_records' AND COLUMN_NAME = ?",
                Integer.class,
                columnName
            );

            if (count == null || count == 0) {
                String sql = String.format("ALTER TABLE inspection_records ADD COLUMN %s %s COMMENT '%s'", 
                    columnName, columnDefinition, comment);
                jdbcTemplate.execute(sql);
                log.info("✅ 添加字段 {} 成功", columnName);
            } else {
                log.debug("🔍 字段 {} 已存在，跳过", columnName);
            }
        } catch (Exception e) {
            log.warn("⚠️ 添加字段 {} 失败: {}", columnName, e.getMessage());
        }
    }

    /**
     * 添加索引（如果不存在）
     */
    private void addIndexIfNotExists(String indexName, String columnName) {
        try {
            // 检查索引是否存在
            Integer count = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'inspection_records' AND INDEX_NAME = ?",
                Integer.class,
                indexName
            );

            if (count == null || count == 0) {
                String sql = String.format("ALTER TABLE inspection_records ADD INDEX %s (%s)", indexName, columnName);
                jdbcTemplate.execute(sql);
                log.info("✅ 添加索引 {} 成功", indexName);
            } else {
                log.debug("🔍 索引 {} 已存在，跳过", indexName);
            }
        } catch (Exception e) {
            log.warn("⚠️ 添加索引 {} 失败: {}", indexName, e.getMessage());
        }
    }

    /**
     * 检查表结构是否正确
     */
    public boolean checkTableStructure() {
        try {
            // 检查关键字段是否存在
            String[] requiredColumns = {"asset_id", "inspection_type", "detection_result", "detection_record_id", "gps_latitude", "gps_longitude"};
            
            for (String column : requiredColumns) {
                Integer count = jdbcTemplate.queryForObject(
                    "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'inspection_records' AND COLUMN_NAME = ?",
                    Integer.class,
                    column
                );
                
                if (count == null || count == 0) {
                    log.warn("⚠️ 缺少字段: {}", column);
                    return false;
                }
            }
            
            log.info("✅ 表结构检查通过");
            return true;
            
        } catch (Exception e) {
            log.error("❌ 检查表结构失败", e);
            return false;
        }
    }
}
