package com.ybda.model.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 交通标志检测DTO
 * 对应视频分析系统返回的单个标志信息
 */
@Data
public class SignDetectionDTO {

    /**
     * 标志类型：ground_marking, overhead_sign, traffic_light, barrier
     */
    @NotBlank(message = "标志类型不能为空")
    private String type;

    /**
     * 标志名称，如"限速标志"、"禁止通行"
     */
    @NotBlank(message = "标志名称不能为空")
    private String name;

    /**
     * 模型推理路径，如"#1→#7→#4"
     */
    private String modelSource;

    /**
     * 标志分类ID
     */
    @NotNull(message = "分类ID不能为空")
    private Integer classId;

    /**
     * 边界框坐标 [x1, y1, x2, y2]
     */
    @NotNull(message = "边界框坐标不能为空")
    @Size(min = 4, max = 4, message = "边界框坐标必须包含4个值")
    private List<Double> bbox;

    /**
     * 跟踪ID，同一物体的唯一标识
     */
    @NotNull(message = "跟踪ID不能为空")
    private Integer trackId;
}
