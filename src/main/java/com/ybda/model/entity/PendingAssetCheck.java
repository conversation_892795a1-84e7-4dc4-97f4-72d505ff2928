package com.ybda.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 待确认资产检查实体类
 * 对应数据库表：pending_asset_checks
 */
@Data
@TableName("pending_asset_checks")
public class PendingAssetCheck {
    
    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /** 设备ID */
    private String deviceId;

    /** 关联的GPS轨迹ID */
    private String gpsTrackId;

    /** 预期的资产ID */
    private String expectedAssetId;

    /** 资产类型 */
    private String assetType;

    /** 资产名称 */
    private String assetName;

    /** GPS位置纬度 */
    private Double gpsLatitude;

    /** GPS位置经度 */
    private Double gpsLongitude;

    /** 经过时间 */
    private LocalDateTime passedTime;

    /** 检查类型：存在性/对应性 */
    private String checkType;

    /** 检查状态：PENDING/CONFIRMED/MISSING/PARTIAL */
    private String status;

    /** 确认的检测记录ID */
    private Long confirmedDetectionId;

    /** 确认时间 */
    private LocalDateTime confirmedTime;

    /** 缺失原因：TIMEOUT/DAMAGED/BLOCKED/REMOVED */
    private String missingReason;

    // 已删除无用字段：verificationTaskId, expectedGroundMarkings, foundGroundMarkings, completionRate
    
    /** 创建时间 */
    private LocalDateTime createdTime;

}
