package com.ybda.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 检测详情实体类
 * 对应数据库表：detection_details
 */
@Data
@TableName("detection_details")
public class DetectionDetail {
    
    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /** 关联检测记录ID */
    private Long detectionRecordId;
    
    /** 标志类型 */
    private String type;
    
    /** 标志名称 */
    private String name;
    
    /** 模型推理路径 */
    private String modelSource;
    
    /** 分类ID */
    private Integer classId;
    
    /** 边界框左上角X坐标 */
    private Double bboxX1;
    
    /** 边界框左上角Y坐标 */
    private Double bboxY1;
    
    /** 边界框右下角X坐标 */
    private Double bboxX2;
    
    /** 边界框右下角Y坐标 */
    private Double bboxY2;
    
    /** 跟踪ID */
    private Integer trackId;
    
    /** 关联资产ID */
    private Long trafficAssetId;
    
    /** 创建时间 */
    private LocalDateTime createdTime;

}
