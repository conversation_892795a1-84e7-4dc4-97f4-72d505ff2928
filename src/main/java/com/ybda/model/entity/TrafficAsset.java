package com.ybda.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ybda.model.enums.GeometryType;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 交通资产实体类
 * 对应数据库表：traffic_assets
 */
@Data
@TableName("traffic_assets")
public class TrafficAsset {
    
    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /** 资产唯一标识 */
    private String assetId;
    
    /** 资产类型：ground_marking/overhead_sign/traffic_light/barrier */
    private String type;
    
    /** 资产名称 */
    private String name;
    
    /** 资产几何类型：POINT/LINE/POLYGON */
    private String geometryType;

    /**
     * 获取几何类型枚举
     */
    public GeometryType getGeometryTypeEnum() {
        try {
            return GeometryType.valueOf(this.geometryType);
        } catch (Exception e) {
            return GeometryType.POINT; // 默认点状
        }
    }

    /**
     * 设置几何类型枚举
     */
    public void setGeometryTypeEnum(GeometryType geometryType) {
        this.geometryType = geometryType.getCode();
    }

    /** 资产纬度（点状资产使用） */
    private Double latitude;

    /** 资产经度（点状资产使用） */
    private Double longitude;

    /** 几何坐标数据（JSON格式存储坐标数组） */
    private String geometryCoordinates;
    
    /** 首次检测时间 */
    private LocalDateTime firstDetectedTime;
    
    /** 最后检测时间 */
    private LocalDateTime lastDetectedTime;
    
    /** 检测次数 */
    private Integer detectionCount;
    
    /** 资产状态：ACTIVE/MISSING/INACTIVE */
    private String status;

    /** 资产是否存在 */
    private Integer available;

    /** 创建时间 */
    private LocalDateTime createdTime;
    
    /** 更新时间 */
    private LocalDateTime updatedTime;

}
