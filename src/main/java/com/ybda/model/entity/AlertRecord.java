package com.ybda.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 报警记录实体类
 * 对应数据库表：alert_records
 */
@Data
@TableName("alert_records")
public class AlertRecord {
    
    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /** 报警类型：ASSET_MISSING/GPS_ABNORMAL/SYSTEM_ERROR */
    private String alertType;
    
    /** 相关资产ID */
    private String assetId;
    
    /** 报警消息 */
    private String alertMessage;
    
    /** 报警详细信息(JSON格式) */
    private String alertDetails;
    
    /** 报警时间 */
    private LocalDateTime alertTime;
    
    /** 报警状态：ACTIVE/RESOLVED/IGNORED */
    private String status;
    
    /** 解决时间 */
    private LocalDateTime resolvedTime;
    
    /** 创建时间 */
    private LocalDateTime createdTime;
    
    /**
     * 报警类型枚举
     */
//    public enum AlertType {
//        ASSET_MISSING("ASSET_MISSING", "资产缺失"),
//        GPS_ABNORMAL("GPS_ABNORMAL", "GPS异常"),
//        SYSTEM_ERROR("SYSTEM_ERROR", "系统错误");
//
//        private final String code;
//        private final String description;
//
//        AlertType(String code, String description) {
//            this.code = code;
//            this.description = description;
//        }
//
//        public String getCode() {
//            return code;
//        }
//
//        public String getDescription() {
//            return description;
//        }
//    }
    
//    /**
//     * 报警状态枚举
//     */
//    public enum AlertStatus {
//        ACTIVE("ACTIVE", "活跃"),
//        RESOLVED("RESOLVED", "已解决"),
//        IGNORED("IGNORED", "已忽略");
//
//        private final String code;
//        private final String description;
//
//        AlertStatus(String code, String description) {
//            this.code = code;
//            this.description = description;
//        }
//
//        public String getCode() {
//            return code;
//        }
//
//        public String getDescription() {
//            return description;
//        }
//    }
    

    


}
