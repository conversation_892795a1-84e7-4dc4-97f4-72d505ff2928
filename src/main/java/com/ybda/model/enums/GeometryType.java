package com.ybda.model.enums;

/**
 * 资产几何类型枚举
 */
public enum GeometryType {
    
    /**
     * 点状资产 - 标志牌、信号灯等
     * 数据格式: {"lat": 39.9042, "lng": 116.4074}
     */
    POINT("POINT", "点状资产"),
    
    /**
     * 线状资产 - 护栏、栏杆、标线等
     * 数据格式: [{"lat": 39.9042, "lng": 116.4074}, {"lat": 39.9043, "lng": 116.4075}, ...]
     */
    LINE("LINE", "线状资产"),
    
    /**
     * 面状资产 - 停车场、路面标记等
     * 数据格式: [{"lat": 39.9042, "lng": 116.4074}, {"lat": 39.9043, "lng": 116.4075}, ...]
     */
    POLYGON("POLYGON", "面状资产");
    
    private final String code;
    private final String description;
    
    GeometryType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据资产类型推断几何类型
     */
    public static GeometryType inferFromAssetType(String assetType) {
        return switch (assetType) {
            case "barrier" -> LINE;           // 护栏通常是线状
            case "ground_marking" -> {
                // 地面标线可能是点状（停车线）或线状（车道线）
                yield POINT; // 默认点状，具体可根据名称判断
            }
            case "overhead_sign" -> POINT;    // 标志牌通常是点状
            case "traffic_light" -> POINT;    // 信号灯通常是点状
            default -> POINT;                 // 默认点状
        };
    }
    
    /**
     * 根据名称进一步判断几何类型
     */
    public static GeometryType inferFromName(String assetType, String name) {
        if ("ground_marking".equals(assetType) && name != null) {
            if (name.contains("车道线") || name.contains("分隔线") || name.contains("导向线")) {
                return LINE;
            }
        }
        if ("barrier".equals(assetType)) {
            return LINE; // 护栏都是线状
        }
        return inferFromAssetType(assetType);
    }
}
