package com.ybda.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ybda.model.entity.DeviceLocation;
import com.ybda.utils.RedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class LocationCacheService {
    @Autowired
    RedisUtils redisUtils;
    private final ObjectMapper objectMapper;

    // 缓存key前缀
    private static final String DEVICE_LOCATION_PREFIX = "device:location:latest:";
    private static final String ALL_DEVICES_LOCATION_KEY = "device:location:all:latest";
    private static final String DEVICE_ID = "id00002";
    // 缓存过期时间（30分钟）
    private static final long CACHE_EXPIRE_MINUTES = 30;

    /**
     * 从缓存获取设备最新位置
     * @return 位置信息，缓存未命中返回null
     */
    public DeviceLocation getLatestLocationFromCache() {
        try {
            String key = DEVICE_LOCATION_PREFIX + DEVICE_ID;
            String locationJson = redisUtils.get(key);

            if (locationJson != null) {
                log.info("从Redis获取到GPS数据: {}", locationJson);
                DeviceLocation location = objectMapper.readValue(locationJson, DeviceLocation.class);
                log.info("从缓存获取设备位置: deviceId={}, lat={}, lng={}, time={}",
                    DEVICE_ID, location.getLat(), location.getLng(), location.getDeviceTime());
                return location;
            }

            log.info("设备位置缓存未命中: deviceId={}", DEVICE_ID);
            return null;
        } catch (com.fasterxml.jackson.core.JsonProcessingException e) {
            log.error("JSON反序列化失败: deviceId={}, error={}", DEVICE_ID, e.getMessage());
            log.error("原始JSON数据可能格式不正确，请检查Redis中的数据格式");
            return null;
        } catch (Exception e) {
            log.error("获取设备位置缓存失败: deviceId={}, error={}", DEVICE_ID, e.getMessage(), e);
            return null;
        }
    }
}
