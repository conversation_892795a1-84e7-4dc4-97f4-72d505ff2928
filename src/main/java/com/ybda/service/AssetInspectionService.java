package com.ybda.service;

import com.ybda.model.entity.DetectionRecord;
import com.ybda.model.entity.InspectionRecord;
import com.ybda.model.entity.TrafficAsset;

import java.util.List;

/**
 * 资产巡检服务接口
 * 负责通用的资产巡检记录管理，包括存在性巡检和对应性巡检
 */
public interface AssetInspectionService {
    
    /**
     * 创建资产存在性巡检记录
     * 当GPS经过资产位置时调用，记录是否检测到该资产
     * 
     * @param asset 资产信息
     * @param detectionRecord 检测记录（如果检测到）
     * @param detected 是否检测到资产
     */
    void createExistenceInspection(TrafficAsset asset, DetectionRecord detectionRecord, boolean detected);
    
    /**
     * 批量处理资产存在性巡检
     * 根据检测记录，为附近的所有资产创建巡检记录
     * 
     * @param detectionRecord 检测记录
     */
    void processAssetExistenceInspections(DetectionRecord detectionRecord);
    
    /**
     * 分析资产的巡检历史，判断资产状态
     * 根据最近N次巡检结果，计算资产的存在概率
     * 
     * @param assetId 资产ID
     * @param recentCount 分析最近几次巡检（默认10次）
     * @return 资产存在概率（0.0-1.0）
     */
    double analyzeAssetExistenceProbability(String assetId, int recentCount);
    
    /**
     * 更新资产状态基于巡检历史
     * 如果连续多次巡检都未检测到，将资产标记为"疑似缺失"或"缺失"
     * 
     * @param assetId 资产ID
     */
    void updateAssetStatusBasedOnInspections(String assetId);
    
    /**
     * 获取资产的巡检统计信息
     * 
     * @param assetId 资产ID
     * @param recentCount 统计最近几次巡检
     * @return 巡检统计信息
     */
    AssetInspectionStats getAssetInspectionStats(String assetId, int recentCount);
    
    /**
     * 清理旧的巡检记录
     * 为每个资产保留最新的N条巡检记录，删除更早的记录
     * 
     * @param keepCount 保留的记录数量
     */
    void cleanupOldInspectionRecords(int keepCount);
    
    /**
     * 资产巡检统计信息
     */
    class AssetInspectionStats {
        private String assetId;
        private int totalInspections;
        private int detectedCount;
        private int missedCount;
        private double detectionRate;
        private String currentStatus;
        private List<InspectionRecord> recentInspections;
        
        // 构造函数
        public AssetInspectionStats(String assetId, int totalInspections, int detectedCount, 
                                  int missedCount, double detectionRate, String currentStatus,
                                  List<InspectionRecord> recentInspections) {
            this.assetId = assetId;
            this.totalInspections = totalInspections;
            this.detectedCount = detectedCount;
            this.missedCount = missedCount;
            this.detectionRate = detectionRate;
            this.currentStatus = currentStatus;
            this.recentInspections = recentInspections;
        }
        
        // Getters
        public String getAssetId() { return assetId; }
        public int getTotalInspections() { return totalInspections; }
        public int getDetectedCount() { return detectedCount; }
        public int getMissedCount() { return missedCount; }
        public double getDetectionRate() { return detectionRate; }
        public String getCurrentStatus() { return currentStatus; }
        public List<InspectionRecord> getRecentInspections() { return recentInspections; }
    }
}
