package com.ybda.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ybda.mapper.TrafficAssetMapper;
import com.ybda.mapper.PendingAssetCheckMapper;
import com.ybda.model.dto.CoordinatePoint;
import com.ybda.utils.GeometryUtils;
import com.ybda.service.AssetIntegrityService;
import com.ybda.model.entity.DeviceLocation;
import com.ybda.model.entity.TrafficAsset;
import com.ybda.model.entity.PendingAssetCheck;
import com.ybda.service.GpsTrackService;
import com.ybda.service.LocationCacheService;
import com.ybda.utils.RedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * GPS轨迹服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GpsTrackServiceImpl implements GpsTrackService {
    
    private final TrafficAssetMapper trafficAssetMapper;
    private final PendingAssetCheckMapper pendingAssetCheckMapper;
    private final LocationCacheService locationCacheService;
    private final AssetIntegrityService assetIntegrityService;
    private final RedisUtils redisUtils;
    private final ObjectMapper objectMapper;
    
    // 配置参数
    private static final String DEFAULT_DEVICE_ID = "id00002";
    private static final double ASSET_PROXIMITY_DISTANCE = 15.0; // 15米
    
    @Override
    public void recordGpsTrack() {
        // 新的设计：直接处理GPS数据，不存储到数据库
        processCurrentGpsLocation();
    }

    /**
     * 直接处理当前GPS位置，执行资产匹配检测
     * 不再需要存储GPS到数据库
     */
    private void processCurrentGpsLocation() {
        try {
            log.info("🌍 开始处理GPS位置");

            // 从LocationCacheService获取最新GPS位置
            DeviceLocation deviceLocation = locationCacheService.getLatestLocationFromCache();

            if (deviceLocation == null) {
                log.info("📍 未获取到GPS位置数据，跳过本次处理");
                return;
            }

            // 检查GPS数据有效性
            if (deviceLocation.getLat() == null || deviceLocation.getLng() == null) {
                log.warn("⚠️ GPS位置数据无效: lat={}, lng={}", deviceLocation.getLat(), deviceLocation.getLng());
                return;
            }

            log.info("📍 获取到GPS位置: lat={}, lng={}, time={}, speed={}km/h",
                deviceLocation.getLat(), deviceLocation.getLng(),
                deviceLocation.getDeviceTime(), deviceLocation.getSpeedKph());

            // 直接执行状态机逻辑
            List<PendingAssetCheck> pendingChecks = processLocationWithStateMachine(deviceLocation);

            // 批量保存待确认记录
            if (!pendingChecks.isEmpty()) {
                pendingAssetCheckMapper.batchInsert(pendingChecks);
                log.info("✅ 创建待确认记录: {} 条", pendingChecks.size());

                // 打印每个待确认记录的详情
                for (PendingAssetCheck check : pendingChecks) {
                    log.info("  📝 新建待确认: assetId={}, type={}, name={}, 位置=({}, {})",
                        check.getExpectedAssetId(), check.getAssetType(), check.getAssetName(),
                        check.getGpsLatitude(), check.getGpsLongitude());
                }
            } else {
                log.info("ℹ️ 无新的待确认记录需要创建");
            }

        } catch (Exception e) {
            log.error("❌ 处理GPS位置失败", e);
        }
    }
    
    /**
     * 使用状态机处理GPS位置
     * 直接基于DeviceLocation，不依赖数据库存储
     */
    private List<PendingAssetCheck> processLocationWithStateMachine(DeviceLocation deviceLocation) {
        List<PendingAssetCheck> pendingChecks = new ArrayList<>();

        try {
            String deviceId = DEFAULT_DEVICE_ID; // 使用默认设备ID
            String redisKey = "gps:assets_state:" + deviceId;

            log.info("🔄 开始状态机处理: deviceId={}", deviceId);

            // 1. 同时获取上一次的资产状态（如果存在）
            Set<String> previousAssetIds = new HashSet<>();
            try {
                String previousAssetsJson = redisUtils.get(redisKey);
                if (previousAssetsJson != null) {
                    List<String> assetIds = objectMapper.readValue(
                        previousAssetsJson, new TypeReference<List<String>>() {});
                    previousAssetIds = new HashSet<>(assetIds);
                    log.info("📋 上次资产状态: {}", previousAssetIds);
                } else {
                    log.info("📋 上次资产状态: 空（首次运行或已过期）");
                }
            } catch (Exception e) {
                log.warn("⚠️ 获取上次资产状态失败，使用空状态: {}", e.getMessage());
            }
            // 2. 获取当前GPS位置附近的资产（支持不同几何类型）
            log.info("🔍 查找{}米范围内的资产（支持点/线/面）", ASSET_PROXIMITY_DISTANCE);
            CoordinatePoint gpsPoint = new CoordinatePoint(deviceLocation.getLat(), deviceLocation.getLng());

            // 先查找点状资产（使用数据库空间查询，效率高）
            List<TrafficAsset> pointAssets = trafficAssetMapper.findNearbyPointAssets(
                deviceLocation.getLat(), deviceLocation.getLng(), ASSET_PROXIMITY_DISTANCE);

            // 再查找线状和面状资产（需要复杂几何计算）
            List<TrafficAsset> complexAssets = trafficAssetMapper.findComplexGeometryAssets();

            // 合并所有匹配的资产
            List<TrafficAsset> currentNearbyAssets = new ArrayList<>();
            currentNearbyAssets.addAll(pointAssets);

            // 对线状和面状资产进行几何判断
            for (TrafficAsset asset : complexAssets) {
                if (GeometryUtils.isPointNearAsset(gpsPoint, asset, ASSET_PROXIMITY_DISTANCE)) {
                    currentNearbyAssets.add(asset);
                }
            }

            Set<String> currentAssetIds = currentNearbyAssets.stream()
                .map(TrafficAsset::getAssetId)
                .collect(Collectors.toSet());

            log.info("📍 当前附近资产: {} (点状:{}, 复杂几何:{})",
                currentAssetIds, pointAssets.size(),
                currentNearbyAssets.size() - pointAssets.size());
            // 3. 立即将当前状态存入Redis，供下一秒使用
            try {
                String currentAssetsJson = objectMapper.writeValueAsString(new ArrayList<>(currentAssetIds));
                redisUtils.setEx(redisKey, currentAssetsJson, 300, java.util.concurrent.TimeUnit.SECONDS); // 5分钟过期
                log.info("💾 已存储当前资产状态到Redis: assets={}", currentAssetIds);
            } catch (Exception e) {
                log.error("❌ 存储当前资产状态到Redis失败: {}", e.getMessage());
            }
            // 4. 状态机逻辑：检查资产集合的变化
            Set<String> newlyEnteredAssets = new HashSet<>(currentAssetIds);
            newlyEnteredAssets.removeAll(previousAssetIds); // 新进入的资产
            Set<String> exitedAssets = new HashSet<>(previousAssetIds);
            exitedAssets.removeAll(currentAssetIds); // 离开的资产

            log.info("🆕 新进入的资产: {}", newlyEnteredAssets);
            log.info("🚪 离开的资产: {}", exitedAssets);
            // 5. 为新进入的资产创建待确认记录和完整性验证任务
            for (String assetId : newlyEnteredAssets) {
                TrafficAsset asset = findAssetById(assetId, currentNearbyAssets);
                if (asset != null) {
                    // 只使用新的完整性验证系统
                    assetIntegrityService.createExistenceCheck(
                        deviceLocation.getDeviceId(),
                        asset.getAssetId(),
                        asset.getType(),
                        asset.getName(),
                        asset.getLatitude(),
                        asset.getLongitude(),
                            deviceLocation.getId()
                    );

                    log.info("🎯 新进入资产范围: assetId={}, type={}, name={} (已创建完整性验证任务)",
                        assetId, asset.getType(), asset.getName());
                } else {
                    log.warn("⚠️ 找不到资产详情: assetId={}", assetId);
                }
            }
            // 6. 记录离开的资产（用于日志）
            for (String assetId : exitedAssets) {
                log.info("👋 离开资产范围: assetId={}", assetId);
            }

            if (newlyEnteredAssets.isEmpty() && exitedAssets.isEmpty()) {
                log.info("🔄 资产状态无变化");
            }
            // 7. 不再需要存储GPS轨迹到数据库
        } catch (Exception e) {
            log.error("状态机处理失败: deviceId={}", deviceLocation.getDeviceId(), e);
        }

        return pendingChecks;
    }
    

    
    /**
     * 根据资产ID查找资产对象
     */
    private TrafficAsset findAssetById(String assetId, List<TrafficAsset> assets) {
        return assets.stream()
            .filter(asset -> assetId.equals(asset.getAssetId()))
            .findFirst()
            .orElse(null);
    }
    
    /**
     * 创建待确认资产检查记录
     * 直接基于DeviceLocation，不依赖GPS轨迹数据库记录
     */
    private PendingAssetCheck createPendingAssetCheck(DeviceLocation deviceLocation, TrafficAsset asset) {
        PendingAssetCheck pendingCheck = new PendingAssetCheck();
        pendingCheck.setDeviceId(deviceLocation.getDeviceId());
        pendingCheck.setGpsTrackId(deviceLocation.getId()); // 不再关联GPS轨迹记录
        pendingCheck.setExpectedAssetId(asset.getAssetId());
        pendingCheck.setAssetType(asset.getType());
        pendingCheck.setAssetName(asset.getName());
        pendingCheck.setGpsLatitude(deviceLocation.getLat());
        pendingCheck.setGpsLongitude(deviceLocation.getLng());
        pendingCheck.setPassedTime(deviceLocation.getDeviceTime() != null ?
            deviceLocation.getDeviceTime() : LocalDateTime.now());
        pendingCheck.setStatus("待确认");
        pendingCheck.setCreatedTime(LocalDateTime.now());
        return pendingCheck;
    }


}
