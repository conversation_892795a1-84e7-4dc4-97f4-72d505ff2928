package com.ybda.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ybda.mapper.InspectionRecordMapper;
import com.ybda.model.entity.DetectionRecord;
import com.ybda.model.entity.InspectionRecord;
import com.ybda.service.SignGroundMatchingService;
import com.ybda.utils.SignParsingUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 标牌地面标线匹配服务实现类（重构版）
 * 移除了 SignVerificationTask，直接使用 InspectionRecord
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SignGroundMatchingServiceImpl implements SignGroundMatchingService {

    private final InspectionRecordMapper inspectionRecordMapper;
    private final ObjectMapper objectMapper;

    // 配置参数
    private static final double MATCHING_DISTANCE = 50.0; // 50米范围内认为是同一路口
    private static final int VERIFICATION_TIMEOUT_MINUTES = 15; // 15分钟验证超时

    @Override
    public void processOverheadSignDetection(DetectionRecord detectionRecord, String signName) {
        try {
            log.info("🏷️ 处理空中标牌检测: signName='{}', recordId={}", signName, detectionRecord.getId());

            // 解析标牌车道信息
            List<SignParsingUtils.LaneInfo> laneInfos = SignParsingUtils.parseOverheadSign(signName);

            if (laneInfos.isEmpty()) {
                log.debug("ℹ️ 标牌无车道指示信息，跳过地面验证: signName='{}'", signName);
                return;
            }

            // 查找是否已存在相同位置的进行中巡检
            List<InspectionRecord> existingInspections = inspectionRecordMapper.findNearbyActiveSignInspections(
                detectionRecord.getGpsLatitude(),
                detectionRecord.getGpsLongitude(),
                MATCHING_DISTANCE,
                signName
            );

            InspectionRecord inspection;
            if (!existingInspections.isEmpty()) {
                // 存在进行中的巡检，复用现有巡检
                inspection = existingInspections.get(0);
                log.info("🔄 复用现有巡检记录: inspectionId={}", inspection.getId());

            } else {
                // 创建新的巡检记录
                Integer nextNumber = inspectionRecordMapper.getNextInspectionNumberBySignAndLocation(
                    signName, 
                    detectionRecord.getGpsLatitude(), 
                    detectionRecord.getGpsLongitude(), 
                    MATCHING_DISTANCE
                );
                if (nextNumber == null) nextNumber = 1;

                inspection = new InspectionRecord();
                inspection.setTaskId(null); // 不再使用任务ID
                inspection.setInspectionType("CORRESPONDENCE");
                inspection.setInspectionNumber(nextNumber);
                inspection.setInspectionTime(LocalDateTime.now());
                inspection.setFoundMarkingsJson("[]");
                inspection.setFoundMarkingsCount(0);
                inspection.setExpectedMarkingsCount(laneInfos.size());
                inspection.calculateCompletionRate();
                inspection.setFinished(false);
                inspection.setGpsLatitude(detectionRecord.getGpsLatitude());
                inspection.setGpsLongitude(detectionRecord.getGpsLongitude());
                inspection.setSignName(signName);
                inspection.setExpectedLanesJson(objectMapper.writeValueAsString(laneInfos));
                inspection.setFirstDetectionTime(detectionRecord.getCreatedTime());
                inspection.setVerificationTimeout(LocalDateTime.now().plusMinutes(VERIFICATION_TIMEOUT_MINUTES));
                inspection.setCreatedTime(LocalDateTime.now());

                // 保存到数据库
                inspectionRecordMapper.insert(inspection);

                log.info("⏰ 创建标牌巡检记录: inspectionId={}, 第{}次巡检, 期望车道数={}",
                    inspection.getId(), nextNumber, laneInfos.size());

                // 打印期望的地面标线
                List<String> expectedMarkings = SignParsingUtils.generateExpectedGroundMarkings(laneInfos);
                log.info("📋 期望的地面标线: {}", expectedMarkings);
            }

        } catch (Exception e) {
            log.error("❌ 处理空中标牌检测失败: signName='{}', recordId={}", signName, detectionRecord.getId(), e);
        }
    }

    @Override
    public void processGroundMarkingDetection(DetectionRecord detectionRecord, String markingName) {
        try {
            log.info("🛣️ 处理地面标线检测: markingName='{}', recordId={}", markingName, detectionRecord.getId());

            // 查找附近的进行中标牌巡检
            List<InspectionRecord> nearbyInspections = inspectionRecordMapper.findNearbyActiveSignInspections(
                detectionRecord.getGpsLatitude(),
                detectionRecord.getGpsLongitude(),
                MATCHING_DISTANCE,
                null // 不限制标牌名称
            );

            if (nearbyInspections.isEmpty()) {
                log.debug("ℹ️ 附近没有进行中的标牌巡检");
                return;
            }

            // 🚦 检查是否是路口结束标识（斑马线或停止线）
            if (isIntersectionEndMarking(markingName)) {
                log.info("🏁 检测到路口结束标识: '{}', 结束附近的标牌巡检", markingName);
                finishNearbyInspectionRecords(nearbyInspections, markingName);
                return;
            }

            // 检查地面标线是否匹配任何标牌巡检
            for (InspectionRecord inspection : nearbyInspections) {
                if (inspection.isFinished()) continue;

                // 解析期望的车道信息
                List<SignParsingUtils.LaneInfo> expectedLanes = parseExpectedLanes(inspection.getExpectedLanesJson());

                if (SignParsingUtils.isGroundMarkingMatchingSign(expectedLanes, markingName)) {
                    // 匹配成功，更新当前巡检记录
                    updateInspectionFoundMarkings(inspection, markingName);

                    log.info("✅ 地面标线匹配成功: inspectionId={}, marking='{}', 当前巡检已找到={}/{}, 第{}次巡检",
                        inspection.getId(), markingName, inspection.getFoundMarkingsCount(),
                        inspection.getExpectedMarkingsCount(), inspection.getInspectionNumber());

                    break;
                }
            }

        } catch (Exception e) {
            log.error("❌ 处理地面标线检测失败: markingName='{}', recordId={}", markingName, detectionRecord.getId(), e);
        }
    }

    @Override
    public void checkTimeoutSignVerifications() {
        try {
            // 查询所有超时的巡检记录
            List<InspectionRecord> timeoutInspections = inspectionRecordMapper.selectTimeoutInspections();

            for (InspectionRecord inspection : timeoutInspections) {
                // 完成超时的巡检记录
                inspection.setFinished(true);
                inspection.setEndMarkingName("TIMEOUT");
                inspection.calculateCompletionRate();
                inspectionRecordMapper.updateById(inspection);

                log.warn("⏰ 标牌巡检超时: inspectionId={}, signName='{}', 第{}次巡检 (未检测到路口结束标识)",
                    inspection.getId(), inspection.getSignName(), inspection.getInspectionNumber());

                // 基于历史巡检记录评估是否需要报警
                evaluateInspectionHistory(inspection);
            }

        } catch (Exception e) {
            log.error("❌ 检查超时验证任务失败", e);
        }
    }

    /**
     * 判断是否是路口结束标识
     */
    private boolean isIntersectionEndMarking(String markingName) {
        if (markingName == null) return false;

        String lowerName = markingName.toLowerCase();

        // 斑马线关键词
        if (lowerName.contains("斑马线") || lowerName.contains("人行横道") ||
            lowerName.contains("crosswalk") || lowerName.contains("zebra")) {
            return true;
        }

        // 停止线关键词
        if (lowerName.contains("停止线") || lowerName.contains("停车线") ||
            lowerName.contains("stop line") || lowerName.contains("stop")) {
            return true;
        }

        return false;
    }

    /**
     * 结束附近的巡检记录
     */
    private void finishNearbyInspectionRecords(List<InspectionRecord> inspections, String endMarkingName) {
        for (InspectionRecord inspection : inspections) {
            if (inspection.isFinished()) continue;

            // 完成巡检记录
            inspection.setFinished(true);
            inspection.setEndMarkingName(endMarkingName);
            inspection.calculateCompletionRate();
            inspectionRecordMapper.updateById(inspection);

            // 判断巡检结果
            int foundCount = inspection.getFoundMarkingsCount();
            int expectedCount = inspection.getExpectedMarkingsCount();
            int successThreshold = Math.max(1, expectedCount / 2);

            if (foundCount >= successThreshold) {
                log.info("🎉 第{}次巡查验证成功: 匹配度={}/{}, 结束标识='{}'",
                    inspection.getInspectionNumber(), foundCount, expectedCount, endMarkingName);
            } else {
                log.warn("⚠️ 第{}次巡查验证不完整: 匹配度={}/{}, 结束标识='{}'",
                    inspection.getInspectionNumber(), foundCount, expectedCount, endMarkingName);
            }

            // 评估历史记录
            evaluateInspectionHistory(inspection);
        }
    }

    /**
     * 更新巡检记录中找到的标线
     */
    private void updateInspectionFoundMarkings(InspectionRecord inspection, String markingName) {
        try {
            // 解析已找到的标线
            List<String> foundMarkings = parseFoundMarkings(inspection.getFoundMarkingsJson());

            // 避免重复添加
            if (!foundMarkings.contains(markingName)) {
                foundMarkings.add(markingName);

                // 更新记录
                inspection.setFoundMarkingsJson(objectMapper.writeValueAsString(foundMarkings));
                inspection.setFoundMarkingsCount(foundMarkings.size());
                inspection.calculateCompletionRate();
                inspectionRecordMapper.updateById(inspection);
            }

        } catch (Exception e) {
            log.error("❌ 更新巡检找到的标线失败: inspectionId={}", inspection.getId(), e);
        }
    }

    /**
     * 解析期望的车道信息
     */
    private List<SignParsingUtils.LaneInfo> parseExpectedLanes(String expectedLanesJson) {
        try {
            if (expectedLanesJson == null || expectedLanesJson.trim().isEmpty()) {
                return new ArrayList<>();
            }
            return objectMapper.readValue(expectedLanesJson, new TypeReference<List<SignParsingUtils.LaneInfo>>() {});
        } catch (Exception e) {
            log.warn("⚠️ 解析期望车道信息失败: {}", expectedLanesJson, e);
            return new ArrayList<>();
        }
    }

    /**
     * 解析已找到的标线
     */
    private List<String> parseFoundMarkings(String foundMarkingsJson) {
        try {
            if (foundMarkingsJson == null || foundMarkingsJson.trim().isEmpty() || "[]".equals(foundMarkingsJson.trim())) {
                return new ArrayList<>();
            }
            return objectMapper.readValue(foundMarkingsJson, new TypeReference<List<String>>() {});
        } catch (Exception e) {
            log.warn("⚠️ 解析已找到标线失败: {}", foundMarkingsJson, e);
            return new ArrayList<>();
        }
    }

    /**
     * 评估巡检历史记录
     */
    private void evaluateInspectionHistory(InspectionRecord inspection) {
        try {
            // 查询该标牌位置的最近3次巡检记录
            List<InspectionRecord> recentInspections = inspectionRecordMapper.findNearbyActiveSignInspections(
                inspection.getGpsLatitude(),
                inspection.getGpsLongitude(),
                MATCHING_DISTANCE,
                inspection.getSignName()
            );

            // 这里可以添加基于历史记录的报警逻辑
            // 比如：连续3次匹配度都很低，则触发报警

        } catch (Exception e) {
            log.error("❌ 评估巡检历史记录失败: inspectionId={}", inspection.getId(), e);
        }
    }
}
