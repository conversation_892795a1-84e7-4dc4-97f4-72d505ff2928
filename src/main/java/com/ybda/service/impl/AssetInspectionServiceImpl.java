package com.ybda.service.impl;

import com.ybda.mapper.DetectionDetailMapper;
import com.ybda.mapper.InspectionRecordMapper;
import com.ybda.mapper.TrafficAssetMapper;
import com.ybda.model.entity.DetectionDetail;
import com.ybda.model.entity.DetectionRecord;
import com.ybda.model.entity.InspectionRecord;
import com.ybda.model.entity.TrafficAsset;
import com.ybda.service.AssetInspectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 资产巡检服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AssetInspectionServiceImpl implements AssetInspectionService {
    
    private final InspectionRecordMapper inspectionRecordMapper;
    private final TrafficAssetMapper trafficAssetMapper;
    private final DetectionDetailMapper detectionDetailMapper;
    
    // 配置参数
    private static final double MATCHING_DISTANCE = 50.0; // 50米范围内认为是同一位置
    private static final int DEFAULT_RECENT_COUNT = 10; // 默认分析最近10次巡检
    private static final double MISSING_THRESHOLD = 0.2; // 检测率低于20%认为可能缺失
    private static final double CONFIRMED_MISSING_THRESHOLD = 0.1; // 检测率低于10%确认缺失
    
    @Override
    public void createExistenceInspection(TrafficAsset asset, DetectionRecord detectionRecord, boolean detected) {
        try {
            // 检查是否已存在近期的巡检记录（避免重复创建）
            if (hasRecentInspection(asset.getAssetId())) {
                log.debug("🔍 资产已有近期巡检记录，跳过创建: assetId={}", asset.getAssetId());
                return;
            }

            // 获取下一个巡检次数
            Integer nextNumber = inspectionRecordMapper.getNextInspectionNumberByAsset(asset.getAssetId());
            if (nextNumber == null) nextNumber = 1;

            // 创建巡检记录
            InspectionRecord inspection = new InspectionRecord();
            inspection.setTaskId(null); // 存在性巡检不需要验证任务ID
            inspection.setAssetId(asset.getAssetId());
            inspection.setInspectionType("存在性");
            inspection.setInspectionNumber(nextNumber);
            inspection.setInspectionTime(LocalDateTime.now());
            inspection.setDetected(detected);
            inspection.setExpectedMarkingsCount(1); // 存在性巡检期望检测到1个资产
            inspection.setFoundMarkingsCount(detected ? 1 : 0);
            
            if (detected && detectionRecord != null) {
                inspection.setDetectionRecordId(detectionRecord.getId());
                inspection.setGpsLatitude(detectionRecord.getGpsLatitude());
                inspection.setGpsLongitude(detectionRecord.getGpsLongitude());
            } else {
                // 未检测到时，使用资产的位置信息
                inspection.setGpsLatitude(asset.getLatitude());
                inspection.setGpsLongitude(asset.getLongitude());
            }
            
            inspection.calculateCompletionRate();
            inspection.setFinished(true); // 存在性巡检立即完成
            inspection.setCreatedTime(LocalDateTime.now());
            
            inspectionRecordMapper.insert(inspection);
            
            log.info("📋 创建资产存在性巡检记录: assetId={}, 第{}次巡检, 检测结果={}", 
                asset.getAssetId(), nextNumber, detected ? "检测到" : "未检测到");
                
        } catch (Exception e) {
            log.error("❌ 创建资产存在性巡检记录失败: assetId={}", asset.getAssetId(), e);
        }
    }
    
    @Override
    public void processAssetExistenceInspections(DetectionRecord detectionRecord) {
        try {
            if (!detectionRecord.hasValidGpsLocation()) {
                return;
            }
            
            // 查找附近的所有资产
            List<TrafficAsset> nearbyAssets = trafficAssetMapper.findNearbyAssets(
                detectionRecord.getGpsLatitude(),
                detectionRecord.getGpsLongitude(),
                MATCHING_DISTANCE
            );
            
            if (nearbyAssets.isEmpty()) {
                log.debug("📍 附近没有资产需要巡检: location=({}, {})", 
                    detectionRecord.getGpsLatitude(), detectionRecord.getGpsLongitude());
                return;
            }
            
            // 获取本次检测到的资产详情
            List<DetectionDetail> detectionDetails = detectionDetailMapper
                .selectByDetectionRecordId(detectionRecord.getId());
            
            log.info("🔍 开始处理资产存在性巡检: 附近资产{}个, 检测到{}个", 
                nearbyAssets.size(), detectionDetails.size());
            
            // 为每个附近的资产创建巡检记录
            for (TrafficAsset asset : nearbyAssets) {
                // 检查是否检测到该资产
                boolean detected = detectionDetails.stream()
                    .anyMatch(detail -> 
                        asset.getType().equals(detail.getType()) && 
                        isAssetNameMatching(asset.getName(), detail.getName())
                    );
                
                // 创建巡检记录
                createExistenceInspection(asset, detectionRecord, detected);
                
                // 如果检测到了，更新资产的最后检测时间
                if (detected) {
                    trafficAssetMapper.updateDetectionInfo(
                        asset.getAssetId(),
                        detectionRecord.getCreatedTime(),
                        asset.getDetectionCount() + 1
                    );
                }
            }
            
            log.info("✅ 完成资产存在性巡检处理: 共处理{}个资产", nearbyAssets.size());
            
        } catch (Exception e) {
            log.error("❌ 处理资产存在性巡检失败: detectionId={}", detectionRecord.getId(), e);
        }
    }
    
    @Override
    public double analyzeAssetExistenceProbability(String assetId, int recentCount) {
        try {
            List<InspectionRecord> recentInspections = inspectionRecordMapper
                .selectLatestByAssetId(assetId, recentCount);
            
            if (recentInspections.isEmpty()) {
                return 1.0; // 没有巡检记录，假设存在
            }
            
            long detectedCount = recentInspections.stream()
                .filter(InspectionRecord::isDetected)
                .count();
            
            double probability = (double) detectedCount / recentInspections.size();
            
            log.debug("📊 资产存在概率分析: assetId={}, 最近{}次巡检, 检测到{}次, 概率={:.2f}", 
                assetId, recentInspections.size(), detectedCount, probability);
            
            return probability;
            
        } catch (Exception e) {
            log.error("❌ 分析资产存在概率失败: assetId={}", assetId, e);
            return 1.0; // 出错时假设存在
        }
    }
    
    @Override
    public void updateAssetStatusBasedOnInspections(String assetId) {
        try {
            double probability = analyzeAssetExistenceProbability(assetId, DEFAULT_RECENT_COUNT);
            
            String newStatus;
            if (probability >= 0.8) {
                newStatus = "正常";
            } else if (probability >= MISSING_THRESHOLD) {
                newStatus = "疑似缺失";
            } else {
                newStatus = "缺失";
            }
            
            // 更新资产状态
            trafficAssetMapper.updateStatus(assetId, newStatus);
            
            log.info("📈 基于巡检历史更新资产状态: assetId={}, 检测概率={:.2f}, 新状态={}", 
                assetId, probability, newStatus);
                
        } catch (Exception e) {
            log.error("❌ 更新资产状态失败: assetId={}", assetId, e);
        }
    }
    
    @Override
    public AssetInspectionStats getAssetInspectionStats(String assetId, int recentCount) {
        try {
            List<InspectionRecord> recentInspections = inspectionRecordMapper
                .selectLatestByAssetId(assetId, recentCount);
            
            int totalInspections = recentInspections.size();
            int detectedCount = (int) recentInspections.stream()
                .filter(InspectionRecord::isDetected)
                .count();
            int missedCount = totalInspections - detectedCount;
            double detectionRate = totalInspections > 0 ? (double) detectedCount / totalInspections : 0.0;
            
            // 获取资产当前状态
            TrafficAsset asset = trafficAssetMapper.selectByAssetId(assetId);
            String currentStatus = asset != null ? asset.getStatus() : "未知";
            
            return new AssetInspectionStats(assetId, totalInspections, detectedCount, 
                missedCount, detectionRate, currentStatus, recentInspections);
                
        } catch (Exception e) {
            log.error("❌ 获取资产巡检统计失败: assetId={}", assetId, e);
            return new AssetInspectionStats(assetId, 0, 0, 0, 0.0, "未知", List.of());
        }
    }
    
    @Override
    public void cleanupOldInspectionRecords(int keepCount) {
        try {
            // 获取所有有巡检记录的资产
            List<String> assetIds = inspectionRecordMapper.selectByInspectionType("EXISTENCE")
                .stream()
                .map(InspectionRecord::getAssetId)
                .distinct()
                .toList();
            
            int cleanedCount = 0;
            for (String assetId : assetIds) {
                int deleted = inspectionRecordMapper.deleteOldAssetRecords(assetId, keepCount);
                cleanedCount += deleted;
            }
            
            log.info("🧹 清理旧巡检记录完成: 共清理{}条记录, 每个资产保留最新{}条", cleanedCount, keepCount);
            
        } catch (Exception e) {
            log.error("❌ 清理旧巡检记录失败", e);
        }
    }
    

    
    /**
     * 检查资产名称是否匹配
     */
    private boolean isAssetNameMatching(String assetName, String detectedName) {
        if (assetName == null || detectedName == null) {
            return false;
        }
        
        // 精确匹配
        if (assetName.equals(detectedName)) {
            return true;
        }
        
        // 模糊匹配（包含关系）
        return assetName.contains(detectedName) || detectedName.contains(assetName);
    }

    /**
     * 检查是否已存在近期的巡检记录
     * 避免在短时间内为同一个资产重复创建巡检记录
     */
    private boolean hasRecentInspection(String assetId) {
        try {
            LocalDateTime recentTime = LocalDateTime.now().minusMinutes(2); // 2分钟内

            // 查询近期是否已有该资产的巡检记录
            List<InspectionRecord> recentInspections = inspectionRecordMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<InspectionRecord>()
                    .eq("asset_id", assetId)
                    .eq("inspection_type", "EXISTENCE")
                    .ge("created_time", recentTime)
                    .orderByDesc("created_time")
                    .last("LIMIT 1")
            );

            boolean hasRecent = !recentInspections.isEmpty();

            if (hasRecent) {
                InspectionRecord recentInspection = recentInspections.get(0);
                log.debug("🔍 发现近期巡检记录: assetId={}, 检测结果={}, 创建时间={}",
                    assetId, recentInspection.isDetected() ? "检测到" : "未检测到", recentInspection.getCreatedTime());
            }

            return hasRecent;

        } catch (Exception e) {
            log.warn("⚠️ 检查近期巡检记录失败: assetId={}", assetId, e);
            return false; // 出错时允许创建，避免影响正常流程
        }
    }
}
