package com.ybda.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ybda.mapper.AlertRecordMapper;
import com.ybda.mapper.DetectionDetailMapper;
import com.ybda.mapper.PendingAssetCheckMapper;
import com.ybda.model.entity.AlertRecord;
import com.ybda.model.entity.DetectionDetail;
import com.ybda.model.entity.DetectionRecord;
import com.ybda.model.entity.PendingAssetCheck;
import com.ybda.service.AssetIntegrityService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 资产完整性验证服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AssetIntegrityServiceImpl implements AssetIntegrityService {
    
    private final PendingAssetCheckMapper pendingAssetCheckMapper;
    private final DetectionDetailMapper detectionDetailMapper;
    private final AlertRecordMapper alertRecordMapper;
    private final ObjectMapper objectMapper;
    
    // 配置参数
    private static final int EXISTENCE_TIMEOUT_MINUTES = 2; // 存在性检查超时时间
    private static final int CORRESPONDENCE_TIMEOUT_MINUTES = 3; // 对应性检查超时时间
    private static final double MATCHING_DISTANCE = 50.0; // 匹配距离（米）
    private static final int RECENT_CHECK_MINUTES = 2; // 2分钟内认为是近期检查
    
    @Override
    public void createExistenceCheck(String deviceId, String expectedAssetId, String assetType,
                                     String assetName, Double latitude, Double longitude, String deviceLocationId) {
        try {
            // 检查是否已存在近期的检查记录（避免重复创建）
            if (hasRecentCheck(deviceId, expectedAssetId)) {
                log.debug("🔍 资产已有近期检查记录，跳过创建: deviceId={}, assetId={}", deviceId, expectedAssetId);
                return;
            }

            PendingAssetCheck check = new PendingAssetCheck();
            check.setDeviceId(deviceId);
            check.setGpsTrackId(deviceLocationId);
            check.setExpectedAssetId(expectedAssetId);
            check.setAssetType(assetType);
            check.setAssetName(assetName);
            check.setGpsLatitude(latitude);
            check.setGpsLongitude(longitude);
            check.setPassedTime(LocalDateTime.now());
            check.setCheckType("存在性");
            check.setStatus("等待");
            check.setCreatedTime(LocalDateTime.now());

            pendingAssetCheckMapper.insert(check);

            log.info("🔍 创建存在性检查任务: deviceId={}, assetId={}, assetName='{}'",
                deviceId, expectedAssetId, assetName);

        } catch (Exception e) {
            log.error("❌ 创建存在性检查任务失败: assetId={}", expectedAssetId, e);
        }
    }
    
    @Override
    public void processDetectionResult(DetectionRecord detectionRecord) {
        try {
            // 1. 处理存在性检查确认
            confirmExistenceChecks(detectionRecord);
            
            // 2. 处理对应性检查更新
            updateCorrespondenceChecks(detectionRecord);
            
        } catch (Exception e) {
            log.error("❌ 处理检测结果失败: detectionId={}", detectionRecord.getId(), e);
        }
    }
    
    /**
     * 确认存在性检查
     */
    private void confirmExistenceChecks(DetectionRecord detectionRecord) {
        try {
            if (!detectionRecord.hasValidGpsLocation()) {
                return;
            }
            
            // 查找附近的待确认存在性检查
            List<PendingAssetCheck> nearbyChecks = pendingAssetCheckMapper.findNearbyPendingChecks(
                detectionRecord.getGpsLatitude(),
                detectionRecord.getGpsLongitude(),
                MATCHING_DISTANCE,
                "存在性",
                "等待"
            );
            
            // 获取检测详情
            List<DetectionDetail> detectionDetails = detectionDetailMapper
                .selectByDetectionRecordId(detectionRecord.getId());
            
            for (PendingAssetCheck check : nearbyChecks) {
                // 检查是否有匹配的检测详情
                boolean found = detectionDetails.stream()
                    .anyMatch(detail -> 
                        check.getAssetType().equals(detail.getType()) && 
                        (check.getAssetName().equals(detail.getName()) || 
                         isAssetNameSimilar(check.getAssetName(), detail.getName()))
                    );
                
                if (found) {
                    // 确认检查
                    check.setStatus("已确认");
                    check.setConfirmedDetectionId(detectionRecord.getId());
                    check.setConfirmedTime(LocalDateTime.now());
                    pendingAssetCheckMapper.updateById(check);
                    
                    log.info("✅ 存在性检查确认: checkId={}, assetName='{}'", 
                        check.getId(), check.getAssetName());
                }
            }
            
        } catch (Exception e) {
            log.error("❌ 确认存在性检查失败: detectionId={}", detectionRecord.getId(), e);
        }
    }
    
    /**
     * 更新对应性检查
     */
    private void updateCorrespondenceChecks(DetectionRecord detectionRecord) {
        try {
            if (!detectionRecord.hasValidGpsLocation()) {
                return;
            }
            
            // 查找附近的待确认对应性检查
            List<PendingAssetCheck> nearbyChecks = pendingAssetCheckMapper.findNearbyPendingChecks(
                detectionRecord.getGpsLatitude(),
                detectionRecord.getGpsLongitude(),
                MATCHING_DISTANCE,
                "对应性",
                "等待"
            );
            
            // 获取检测详情
            List<DetectionDetail> detectionDetails = detectionDetailMapper
                .selectByDetectionRecordId(detectionRecord.getId());
            
            for (PendingAssetCheck check : nearbyChecks) {
                // 查找地面标识
                // 对应性检查功能已转移到 SignGroundMatchingService
                // 这里不再处理地面标线的对应性检查
                log.debug("⚠️ 对应性检查功能已转移到 SignGroundMatchingService，跳过处理");
            }
            
        } catch (Exception e) {
            log.error("❌ 更新对应性检查失败: detectionId={}", detectionRecord.getId(), e);
        }
    }
    
    // updateCorrespondenceCheckProgress 方法已删除，功能转移到 SignGroundMatchingService
    
    @Override
    public void checkTimeoutTasks() {
        try {
            LocalDateTime now = LocalDateTime.now();
            
            // 检查存在性检查超时
            LocalDateTime existenceTimeout = now.minusMinutes(EXISTENCE_TIMEOUT_MINUTES);
            List<PendingAssetCheck> timeoutExistenceChecks = pendingAssetCheckMapper
                .findTimeoutChecks("存在性", "等待", existenceTimeout);

            for (PendingAssetCheck check : timeoutExistenceChecks) {
                check.setStatus("缺失");
                check.setMissingReason("超时");
                pendingAssetCheckMapper.updateById(check);
                
                // 触发缺失报警
                triggerMissingAssetAlert(check);
                
                log.warn("⏰ 存在性检查超时: checkId={}, assetName='{}'", 
                    check.getId(), check.getAssetName());
            }
            
            // 检查对应性检查超时
            LocalDateTime correspondenceTimeout = now.minusMinutes(CORRESPONDENCE_TIMEOUT_MINUTES);
            List<PendingAssetCheck> timeoutCorrespondenceChecks = pendingAssetCheckMapper
                .findTimeoutChecks("对应性", "等待", correspondenceTimeout);

            for (PendingAssetCheck check : timeoutCorrespondenceChecks) {
                // 对应性检查功能已转移到 SignGroundMatchingService
                // 这里简化处理：直接标记为超时
                check.setStatus("超时");
                check.setConfirmedTime(now);
                check.setMissingReason("对应性检查超时");
                pendingAssetCheckMapper.updateById(check);

                log.info("⏰ 对应性检查超时处理: checkId={}, 状态=超时 (功能已转移到SignGroundMatchingService)",
                    check.getId());
            }
            
        } catch (Exception e) {
            log.error("❌ 检查超时任务失败", e);
        }
    }
    
    /**
     * 触发缺失资产报警
     */
    private void triggerMissingAssetAlert(PendingAssetCheck check) {
        try {
            AlertRecord alert = new AlertRecord();
            alert.setAlertType("ASSET_MISSING");
            alert.setAssetId(check.getExpectedAssetId());
            alert.setAlertMessage(String.format("资产缺失：期望在位置检测到'%s'，但未发现", check.getAssetName()));
            
            Map<String, Object> details = new HashMap<>();
            details.put("checkId", check.getId());
            details.put("deviceId", check.getDeviceId());
            details.put("assetType", check.getAssetType());
            details.put("assetName", check.getAssetName());
            details.put("expectedLocation", Map.of("lat", check.getGpsLatitude(), "lng", check.getGpsLongitude()));
            details.put("passedTime", check.getPassedTime());
            details.put("missingReason", check.getMissingReason());
            
            alert.setAlertDetails(objectMapper.writeValueAsString(details));
            alert.setStatus("ACTIVE");
            alert.setCreatedTime(LocalDateTime.now());
            
            alertRecordMapper.insert(alert);
            
            log.warn("🚨 触发缺失资产报警: assetName='{}', location=({}, {})", 
                check.getAssetName(), check.getGpsLatitude(), check.getGpsLongitude());
            
        } catch (Exception e) {
            log.error("❌ 触发缺失资产报警失败: checkId={}", check.getId(), e);
        }
    }
    
    /**
     * 触发部分对应性报警
     */
    private void triggerPartialCorrespondenceAlert(PendingAssetCheck check) {
        try {
            AlertRecord alert = new AlertRecord();
            alert.setAlertType("CORRESPONDENCE_PARTIAL");
            alert.setAssetId(check.getExpectedAssetId());
            alert.setAlertMessage("标牌地面对应性不完整：部分地面标识缺失");
            
            Map<String, Object> details = new HashMap<>();
            details.put("checkId", check.getId());
            details.put("deviceId", check.getDeviceId());
            details.put("assetId", check.getExpectedAssetId());
            details.put("assetName", check.getAssetName());
            details.put("checkType", check.getCheckType());
            details.put("location", Map.of("lat", check.getGpsLatitude(), "lng", check.getGpsLongitude()));

            alert.setAlertDetails(objectMapper.writeValueAsString(details));
            alert.setStatus("ACTIVE");
            alert.setCreatedTime(LocalDateTime.now());

            alertRecordMapper.insert(alert);

            log.warn("🚨 触发对应性检查报警: checkId={} (功能已转移到SignGroundMatchingService)",
                check.getId());
            
        } catch (Exception e) {
            log.error("❌ 触发部分对应性报警失败: checkId={}", check.getId(), e);
        }
    }
    
    /**
     * 检查资产名称是否相似
     */
    private boolean isAssetNameSimilar(String expected, String actual) {
        if (expected == null || actual == null) {
            return false;
        }
        
        // 简单的相似性检查，可以根据需要扩展
        return expected.contains(actual) || actual.contains(expected) ||
               expected.toLowerCase().contains(actual.toLowerCase()) ||
               actual.toLowerCase().contains(expected.toLowerCase());
    }

    /**
     * 检查是否已存在近期的检查记录
     * 避免在短时间内为同一个资产重复创建检查记录
     */
    private boolean hasRecentCheck(String deviceId, String expectedAssetId) {
        try {
            LocalDateTime recentTime = LocalDateTime.now().minusMinutes(RECENT_CHECK_MINUTES);

            // 查询近期是否已有该资产的检查记录
            List<PendingAssetCheck> recentChecks = pendingAssetCheckMapper.selectList(
                new QueryWrapper<PendingAssetCheck>()
                    .eq("device_id", deviceId)
                    .eq("expected_asset_id", expectedAssetId)
                    .ge("created_time", recentTime)
                    .orderByDesc("created_time")
                    .last("LIMIT 1")
            );

            boolean hasRecent = !recentChecks.isEmpty();

            if (hasRecent) {
                PendingAssetCheck recentCheck = recentChecks.get(0);
                log.debug("🔍 发现近期检查记录: deviceId={}, assetId={}, 状态={}, 创建时间={}",
                    deviceId, expectedAssetId, recentCheck.getStatus(), recentCheck.getCreatedTime());
            }

            return hasRecent;

        } catch (Exception e) {
            log.warn("⚠️ 检查近期记录失败: deviceId={}, assetId={}", deviceId, expectedAssetId, e);
            return false; // 出错时允许创建，避免影响正常流程
        }
    }
}
