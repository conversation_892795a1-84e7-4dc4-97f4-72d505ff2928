package com.ybda.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ybda.mapper.DetectionRecordMapper;
import com.ybda.mapper.DetectionDetailMapper;
import com.ybda.mapper.PendingAssetCheckMapper;
import com.ybda.mapper.TrafficAssetMapper;
import com.ybda.model.dto.CoordinatePoint;
import com.ybda.model.enums.GeometryType;
import com.ybda.service.SignGroundMatchingService;
import com.ybda.service.AssetIntegrityService;
import com.ybda.service.AssetInspectionService;
import com.ybda.model.dto.DetectionRequestDTO;
import com.ybda.model.dto.SignDetectionDTO;
import com.ybda.model.entity.*;
import com.ybda.service.DetectionService;
import com.ybda.service.LocationCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 检测服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DetectionServiceImpl implements DetectionService {
    
    private final DetectionRecordMapper detectionRecordMapper;
    private final DetectionDetailMapper detectionDetailMapper;
    private final PendingAssetCheckMapper pendingAssetCheckMapper;
    private final TrafficAssetMapper trafficAssetMapper;
    private final LocationCacheService locationCacheService;
    private final SignGroundMatchingService signGroundMatchingService;
    private final AssetIntegrityService assetIntegrityService;
    private final AssetInspectionService assetInspectionService;
    private final ObjectMapper objectMapper;
    
    // 配置参数
    private static final double MATCHING_DISTANCE = 15.0; // 15米匹配距离
    private static final int TIME_WINDOW_MINUTES = 5; // 5分钟时间窗口
    
    @Override
    @Transactional
    public Map<String, Object> processDetectionRequest(DetectionRequestDTO detectionRequest) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("🚀 开始处理检测请求: frameId={}, timestamp={}, signsCount={}",
                detectionRequest.getFrameId(), detectionRequest.getTimestamp(),
                detectionRequest.getSigns() != null ? detectionRequest.getSigns().size() : 0);

            // 1. 获取当前GPS位置
            log.info("📍 步骤1: 获取当前GPS位置");
            DeviceLocation currentLocation = locationCacheService.getLatestLocationFromCache();

            if (currentLocation != null) {
                log.info("✅ GPS位置获取成功: lat={}, lng={}, time={}",
                    currentLocation.getLat(), currentLocation.getLng(), currentLocation.getDeviceTime());
            } else {
                log.warn("⚠️ 未获取到GPS位置数据，将使用空位置信息");
            }

            // 2. 创建检测记录
            log.info("📝 步骤2: 创建检测记录");
            DetectionRecord detectionRecord = createDetectionRecord(detectionRequest, currentLocation);
            int recordResult = detectionRecordMapper.insert(detectionRecord);

            if (recordResult <= 0) {
                throw new RuntimeException("保存检测记录失败");
            }
            log.info("✅ 检测记录创建成功: recordId={}, gpsLocation=({}, {})",
                detectionRecord.getId(), detectionRecord.getGpsLatitude(), detectionRecord.getGpsLongitude());

            // 3. 创建检测详情
            log.info("📋 步骤3: 创建检测详情");
            List<DetectionDetail> detectionDetails = createDetectionDetails(
                detectionRecord.getId(), detectionRequest.getSigns());

            if (!detectionDetails.isEmpty()) {
                detectionDetailMapper.batchInsert(detectionDetails);
                log.info("✅ 检测详情创建成功: {} 条详情记录", detectionDetails.size());

                // 打印每个检测详情
                for (DetectionDetail detail : detectionDetails) {
                    log.info("  - 检测到: type={}, name={}, classId={}, trackId={}",
                        detail.getType(), detail.getName(), detail.getClassId(), detail.getTrackId());
                }
            } else {
                log.info("ℹ️ 无检测详情需要创建");
            }

            // 4. 确认待确认的资产检查记录
            log.info("🔍 步骤4: 确认待确认的资产检查记录");
            confirmPendingAssetChecks(detectionRecord);

            // 5. 处理新增资产
            log.info("🆕 步骤5: 处理新增资产");
            processNewAssets(detectionRecord);

            // 6. 建立检测记录与资产的对应关系
            log.debug("🔗 步骤6: 建立检测记录与资产的对应关系");
            establishAssetRelations(detectionRecord, detectionDetails);

            // 7. 处理标牌地面对应关系验证
            log.debug("🏷️ 步骤7: 处理标牌地面对应关系验证");
            processSignGroundMatching(detectionRecord, detectionDetails);

            // 8. 处理完整性验证
            log.debug("🔍 步骤8: 处理完整性验证");
            assetIntegrityService.processDetectionResult(detectionRecord);

            // 9. 处理资产存在性巡检
            log.debug("📋 步骤9: 处理资产存在性巡检");
            assetInspectionService.processAssetExistenceInspections(detectionRecord);

            // 10. 更新处理状态
            log.info("✏️ 步骤10: 更新处理状态为已处理");
            detectionRecordMapper.updateProcessStatus(
                detectionRecord.getId(),
                "已处理");

            result.put("success", true);
            result.put("recordId", detectionRecord.getId());
            result.put("detailsCount", detectionDetails.size());
            result.put("imageUrl", detectionRecord.getImageUrl());
            result.put("relatedAssetIds", detectionRecord.getRelatedAssetIds());
            result.put("message", "检测请求处理成功");

            log.info("🎉 检测请求处理完成: frameId={}, recordId={}, detailsCount={}, relatedAssets={}, imageUrl={}",
                detectionRequest.getFrameId(), detectionRecord.getId(), detectionDetails.size(),
                detectionRecord.getRelatedAssetIds() != null ? "已关联" : "无",
                detectionRecord.getImageUrl() != null ? "已保存" : "无");

        } catch (Exception e) {
            log.error("❌ 处理检测请求失败: frameId={}", detectionRequest.getFrameId(), e);
            result.put("success", false);
            result.put("message", "处理失败: " + e.getMessage());
        }

        return result;
    }
    
    /**
     * 创建检测记录
     */
    private DetectionRecord createDetectionRecord(DetectionRequestDTO request, DeviceLocation location) {
        DetectionRecord record = new DetectionRecord();
        record.setFrameId(request.getFrameId());
        record.setTimestamp(request.getTimestamp());
        record.setImageUrl(request.getImageUrl());
        record.setProcessStatus("待处理");
        record.setCreatedTime(LocalDateTime.now());
        
        // 设置GPS位置
        if (location != null && location.getLat() != null && location.getLng() != null) {
            record.setGpsLatitude(location.getLat());
            record.setGpsLongitude(location.getLng());
            record.setDeviceId("DEVICE_001"); // 默认设备ID
        }
        
        // 保存原始JSON数据
        try {
            record.setRawData(objectMapper.writeValueAsString(request));
        } catch (Exception e) {
            log.warn("序列化原始数据失败", e);
        }
        
        return record;
    }
    
    /**
     * 创建检测详情列表
     */
    private List<DetectionDetail> createDetectionDetails(Long recordId, List<SignDetectionDTO> signs) {
        List<DetectionDetail> details = new ArrayList<>();
        
        for (SignDetectionDTO sign : signs) {
            DetectionDetail detail = new DetectionDetail();
            detail.setDetectionRecordId(recordId);
            detail.setType(sign.getType());
            detail.setName(sign.getName());
            detail.setModelSource(sign.getModelSource());
            detail.setClassId(sign.getClassId());
            detail.setTrackId(sign.getTrackId());
            detail.setCreatedTime(LocalDateTime.now());
            
            // 设置边界框坐标
            if (sign.getBbox() != null && sign.getBbox().size() >= 4) {
                detail.setBboxX1(sign.getBbox().get(0));
                detail.setBboxY1(sign.getBbox().get(1));
                detail.setBboxX2(sign.getBbox().get(2));
                detail.setBboxY2(sign.getBbox().get(3));
            }
            
            details.add(detail);
        }
        
        return details;
    }
    
    @Override
    public void confirmPendingAssetChecks(DetectionRecord detectionRecord) {
        log.info("🔍 开始确认待确认记录: recordId={}", detectionRecord.getId());

        if (!detectionRecord.hasValidGpsLocation()) {
            log.warn("⚠️ 检测记录缺少有效GPS位置，无法确认待确认记录: recordId={}", detectionRecord.getId());
            return;
        }

        try {
            // 计算时间窗口
            LocalDateTime detectionTime = detectionRecord.getCreatedTime();
            LocalDateTime startTime = detectionTime.minusMinutes(TIME_WINDOW_MINUTES);
            LocalDateTime endTime = detectionTime.plusMinutes(TIME_WINDOW_MINUTES);

            log.info("📅 时间窗口: {} ~ {} ({}分钟窗口)", startTime, endTime, TIME_WINDOW_MINUTES);
            log.info("📍 搜索位置: lat={}, lng={}, 距离={}米",
                detectionRecord.getGpsLatitude(), detectionRecord.getGpsLongitude(), MATCHING_DISTANCE);

            // 查找附近的待确认记录按时间
            List<PendingAssetCheck> nearbyChecks = pendingAssetCheckMapper.searchForNearbyRecordsToBeConfirmedByTime(
                detectionRecord.getGpsLatitude(),
                detectionRecord.getGpsLongitude(),
                MATCHING_DISTANCE,
                startTime,
                endTime
            );

            log.info("🔍 找到附近的待确认记录: {} 条", nearbyChecks.size());

            if (nearbyChecks.isEmpty()) {
                log.info("ℹ️ 未找到匹配的待确认记录: recordId={}", detectionRecord.getId());
                return;
            }

            // 获取检测详情，用于匹配资产类型
            List<DetectionDetail> detectionDetails = detectionDetailMapper
                .selectByDetectionRecordId(detectionRecord.getId());

            Set<String> detectedTypes = new HashSet<>();
            for (DetectionDetail detail : detectionDetails) {
                detectedTypes.add(detail.getType());
            }

            log.info("🏷️ 检测到的资产类型: {}", detectedTypes);

            // 确认匹配的待确认记录
            List<Long> confirmedIds = new ArrayList<>();
            for (PendingAssetCheck check : nearbyChecks) {
                log.info("🔄 检查待确认记录: checkId={}, assetId={}, assetType={}, 经过时间={}",
                    check.getId(), check.getExpectedAssetId(), check.getAssetType(), check.getPassedTime());

                if (detectedTypes.contains(check.getAssetType())) {
                    confirmedIds.add(check.getId());
                    log.info("✅ 匹配成功，确认待确认记录: checkId={}, assetId={}, assetType={}",
                        check.getId(), check.getExpectedAssetId(), check.getAssetType());
                } else {
                    log.info("❌ 类型不匹配: 期望={}, 检测到={}", check.getAssetType(), detectedTypes);
                }
            }

            if (!confirmedIds.isEmpty()) {
                pendingAssetCheckMapper.batchConfirmChecks(
                    confirmedIds, detectionRecord.getId(), LocalDateTime.now());
                log.info("🎉 成功确认待确认记录: {} 条", confirmedIds.size());
            } else {
                log.info("ℹ️ 没有匹配的待确认记录需要确认");
            }

        } catch (Exception e) {
            log.error("❌ 确认待确认记录失败: recordId={}", detectionRecord.getId(), e);
        }
    }
    
    @Override
    public void processNewAssets(DetectionRecord detectionRecord) {
        if (!detectionRecord.hasValidGpsLocation()) {
            return;
        }
        
        try {
            // 获取检测详情
            List<DetectionDetail> detectionDetails = detectionDetailMapper
                .selectByDetectionRecordId(detectionRecord.getId());
            
            for (DetectionDetail detail : detectionDetails) {
                // 查找附近是否已有相同类型的资产
                List<TrafficAsset> nearbyAssets = trafficAssetMapper.findNearbyAssets(
                    detectionRecord.getGpsLatitude(),
                    detectionRecord.getGpsLongitude(),
                    MATCHING_DISTANCE
                );
                
                boolean assetExists = nearbyAssets.stream()
                    .anyMatch(asset -> asset.getType().equals(detail.getType()) 
                        && asset.getName().equals(detail.getName()));
                
                if (!assetExists) {
                    // 创建新资产
                    TrafficAsset newAsset = createNewAsset(detectionRecord, detail);
                    trafficAssetMapper.insert(newAsset);

                    // 关联检测详情
                    detectionDetailMapper.updateTrafficAssetId(detail.getId(), newAsset.getId());

                    // 创建已确认的检查记录（因为是刚检测到的，说明资产确实存在）
                    createConfirmedAssetCheck(detectionRecord, newAsset);

                    log.info("🆕 发现新资产: type={}, name={}, location=({}, {})",
                        newAsset.getType(), newAsset.getName(),
                        newAsset.getLatitude(), newAsset.getLongitude());
                } else {
                    // 更新现有资产的检测信息
                    TrafficAsset existingAsset = nearbyAssets.stream()
                        .filter(asset -> asset.getType().equals(detail.getType()) 
                            && asset.getName().equals(detail.getName()))
                        .findFirst()
                        .orElse(null);
                    
                    if (existingAsset != null) {
                        // 🔗 对于线状/面状资产，合并新的GPS坐标点
                        if (detectionRecord.hasValidGpsLocation()) {
                            GeometryType geometryType = existingAsset.getGeometryTypeEnum();

                            if (geometryType == GeometryType.LINE || geometryType == GeometryType.POLYGON) {
                                mergeCoordinateToAsset(existingAsset, detectionRecord.getGpsLatitude(), detectionRecord.getGpsLongitude());
                            }
                        }

                        // 更新检测次数和最后检测时间
                        trafficAssetMapper.updateDetectionInfo(
                            existingAsset.getAssetId(),
                            detectionRecord.getCreatedTime(),
                            existingAsset.getDetectionCount() + 1
                        );

                        // 关联检测详情
                        detectionDetailMapper.updateTrafficAssetId(detail.getId(), existingAsset.getId());

                        log.info("🔄 更新现有资产: assetId={}, geometryType={}",
                            existingAsset.getAssetId(), existingAsset.getGeometryTypeEnum().getDescription());
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("处理新增资产失败: recordId={}", detectionRecord.getId(), e);
        }
    }
    
    /**
     * 创建新资产（支持智能几何类型推断）
     */
    private TrafficAsset createNewAsset(DetectionRecord detectionRecord, DetectionDetail detail) {
        TrafficAsset asset = new TrafficAsset();
        asset.setAssetId(generateAssetId(detail.getType()));
        asset.setType(detail.getType());
        asset.setName(detail.getName());

        // 🔍 智能推断几何类型
        GeometryType geometryType = GeometryType.inferFromName(detail.getType(), detail.getName());
        asset.setGeometryTypeEnum(geometryType);

        log.info("🔍 智能推断几何类型: type={}, name={} → geometryType={}",
            detail.getType(), detail.getName(), geometryType.getDescription());

        // 📍 根据几何类型设置坐标
        if (detectionRecord.hasValidGpsLocation()) {
            if (geometryType == GeometryType.POINT) {
                // 点状资产：直接使用GPS坐标
                asset.setLatitude(detectionRecord.getGpsLatitude());
                asset.setLongitude(detectionRecord.getGpsLongitude());
                log.debug("📍 点状资产坐标: ({}, {})",
                    detectionRecord.getGpsLatitude(), detectionRecord.getGpsLongitude());
            } else {
                // 线状/面状资产：初始化为单点，后续会在updateExistingAsset中扩展
                String coordinates = String.format(
                    "[{\"lat\":%.8f,\"lng\":%.8f}]",
                    detectionRecord.getGpsLatitude(),
                    detectionRecord.getGpsLongitude()
                );
                asset.setGeometryCoordinates(coordinates);
                log.info("📍 {}初始坐标: {}", geometryType.getDescription(), coordinates);
            }
        }

        asset.setFirstDetectedTime(detectionRecord.getCreatedTime());
        asset.setLastDetectedTime(detectionRecord.getCreatedTime());
        asset.setDetectionCount(1);
        asset.setStatus("正常");
        asset.setAvailable(1);
        asset.setCreatedTime(LocalDateTime.now());
        asset.setUpdatedTime(LocalDateTime.now());

        return asset;
    }
    
    /**
     * 生成资产ID
     */
    private String generateAssetId(String type) {
        String prefix = switch (type) {
            case "ground_marking" -> "GM";
            case "overhead_sign" -> "OS";
            case "traffic_light" -> "TL";
            case "barrier" -> "BR";
            default -> "AS";
        };
        return prefix + "_" + System.currentTimeMillis();
    }

    /**
     * 将新的坐标点合并到线状/面状资产中
     */
    private void mergeCoordinateToAsset(TrafficAsset asset, Double newLat, Double newLng) {
        try {
            List<CoordinatePoint> existingPoints = new ArrayList<>();

            // 解析现有坐标
            if (asset.getGeometryCoordinates() != null && !asset.getGeometryCoordinates().trim().isEmpty()) {
                existingPoints = objectMapper.readValue(
                    asset.getGeometryCoordinates(),
                    new TypeReference<List<CoordinatePoint>>() {}
                );
            }

            CoordinatePoint newPoint = new CoordinatePoint(newLat, newLng);

            // 检查新点是否与现有点重复（距离小于5米认为是重复）
            boolean isDuplicate = existingPoints.stream()
                .anyMatch(point -> point.distanceTo(newPoint) < 5.0);

            if (!isDuplicate) {
                existingPoints.add(newPoint);

                // 按照合理的顺序排序坐标点（可以根据实际需求调整排序逻辑）
                sortCoordinatePoints(existingPoints);

                // 更新资产的坐标数据
                String updatedCoordinates = objectMapper.writeValueAsString(existingPoints);
                asset.setGeometryCoordinates(updatedCoordinates);

                // 直接更新数据库中的坐标数据
                trafficAssetMapper.updateGeometryCoordinates(asset.getAssetId(), updatedCoordinates);

                log.info("🔗 {}坐标合并: 新增点({}, {}), 总点数: {}",
                    asset.getGeometryTypeEnum().getDescription(), newLat, newLng, existingPoints.size());
            } else {
                log.debug("⚠️ 坐标点重复，跳过合并: ({}, {})", newLat, newLng);
            }

        } catch (Exception e) {
            log.error("❌ 合并坐标点失败: assetId={}", asset.getAssetId(), e);
        }
    }

    /**
     * 对坐标点进行排序（简单的从西到东、从南到北排序）
     * 实际项目中可能需要更复杂的排序逻辑，比如按照道路方向
     */
    private void sortCoordinatePoints(List<CoordinatePoint> points) {
        points.sort((p1, p2) -> {
            // 先按纬度排序（南到北）
            int latCompare = Double.compare(p1.getLat(), p2.getLat());
            if (latCompare != 0) {
                return latCompare;
            }
            // 纬度相同时按经度排序（西到东）
            return Double.compare(p1.getLng(), p2.getLng());
        });
    }

    /**
     * 建立检测记录与资产的对应关系
     * 直接使用已有的检测详情，避免重复数据库查询
     */
    private void establishAssetRelations(DetectionRecord detectionRecord, List<DetectionDetail> detectionDetails) {
        try {
            if (detectionDetails.isEmpty()) {
                log.debug("ℹ️ 无检测详情，跳过资产关联");
                return;
            }

            // 收集所有已关联的资产ID
            List<String> relatedAssetIds = new ArrayList<>();

            for (DetectionDetail detail : detectionDetails) {
                // 检测详情在processNewAssets步骤中已经关联了资产ID
                if (detail.getTrafficAssetId() != null) {
                    // 根据资产表ID获取资产ID
//                    TrafficAsset asset = trafficAssetMapper.selectById(detail.getTrafficAssetId());
//                    if (asset != null) {
                        relatedAssetIds.add(String.valueOf(detail.getTrafficAssetId()));
                        log.debug("✅ 收集资产关联: 检测详情[{}] -> 资产[{}] ({})",
                            detail.getName(), detail.getTrafficAssetId(), detail.getName());
//                    }
                }
            }

            // 更新检测记录的资产关联信息
            if (!relatedAssetIds.isEmpty()) {
                String relatedAssetIdsJson = objectMapper.writeValueAsString(relatedAssetIds);
                detectionRecordMapper.updateAssetRelation(
                    detectionRecord.getId(),
                    relatedAssetIdsJson
                );

                // 更新DetectionRecord对象的字段，以便在响应中返回
                detectionRecord.setRelatedAssetIds(relatedAssetIdsJson);

                log.info("🔗 检测记录资产关联完成: recordId={}, relatedAssets={}",
                    detectionRecord.getId(), relatedAssetIds);
            } else {
                log.debug("ℹ️ 无资产关联信息");
            }

        } catch (Exception e) {
            log.error("❌ 建立资产关联失败: recordId={}", detectionRecord.getId(), e);
        }
    }

    /**
     * 处理标牌地面对应关系验证
     * 检测空中标牌和地面标线的对应关系
     */
    private void processSignGroundMatching(DetectionRecord detectionRecord, List<DetectionDetail> detectionDetails) {
        try {
            for (DetectionDetail detail : detectionDetails) {
                if ("overhead_sign".equals(detail.getType())) {
                    // 处理空中标牌检测
                    signGroundMatchingService.processOverheadSignDetection(detectionRecord, detail.getName());
                    log.debug("🏷️ 处理空中标牌: name={}", detail.getName());

                } else if ("ground_marking".equals(detail.getType())) {
                    // 处理地面标线检测
                    signGroundMatchingService.processGroundMarkingDetection(detectionRecord, detail.getName());
                    log.debug("🛣️ 处理地面标线: name={}", detail.getName());
                }
            }

        } catch (Exception e) {
            log.error("❌ 处理标牌地面对应关系验证失败: recordId={}", detectionRecord.getId(), e);
        }
    }

    /**
     * 为新检测到的资产创建已确认的检查记录
     * 由于是刚检测到的资产，说明它确实存在，可以直接标记为已确认
     */
    private void createConfirmedAssetCheck(DetectionRecord detectionRecord, TrafficAsset newAsset) {
        try {
            // 检查是否已存在近期的检查记录（避免重复创建）
            if (hasRecentAssetCheck(detectionRecord.getDeviceId(), newAsset.getAssetId())) {
                log.debug("🔍 新资产已有近期检查记录，跳过创建: assetId={}", newAsset.getAssetId());
                return;
            }

            PendingAssetCheck check = new PendingAssetCheck();
            check.setDeviceId(detectionRecord.getDeviceId());
            check.setGpsTrackId(detectionRecord.getFrameId()); // 使用frameId作为轨迹标识
            check.setExpectedAssetId(newAsset.getAssetId());
            check.setAssetType(newAsset.getType());
            check.setAssetName(newAsset.getName());
            check.setGpsLatitude(detectionRecord.getGpsLatitude());
            check.setGpsLongitude(detectionRecord.getGpsLongitude());
            check.setPassedTime(detectionRecord.getCreatedTime());
            check.setCheckType("存在性");
            check.setStatus("已确认"); // 直接设为已确认
            check.setConfirmedDetectionId(detectionRecord.getId());
            check.setConfirmedTime(detectionRecord.getCreatedTime());
            check.setCreatedTime(LocalDateTime.now());

            pendingAssetCheckMapper.insert(check);

            log.info("✅ 为新资产创建已确认检查记录: assetId={}, assetName='{}'",
                newAsset.getAssetId(), newAsset.getName());

        } catch (Exception e) {
            log.error("❌ 创建新资产检查记录失败: assetId={}", newAsset.getAssetId(), e);
        }
    }

    /**
     * 检查是否已存在近期的资产检查记录
     */
    private boolean hasRecentAssetCheck(String deviceId, String assetId) {
        try {
            LocalDateTime recentTime = LocalDateTime.now().minusMinutes(2); // 2分钟内

            // 查询近期是否已有该资产的检查记录
            List<PendingAssetCheck> recentChecks = pendingAssetCheckMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<PendingAssetCheck>()
                    .eq("device_id", deviceId)
                    .eq("expected_asset_id", assetId)
                    .ge("created_time", recentTime)
                    .orderByDesc("created_time")
                    .last("LIMIT 1")
            );

            return !recentChecks.isEmpty();

        } catch (Exception e) {
            log.warn("⚠️ 检查近期资产记录失败: deviceId={}, assetId={}", deviceId, assetId, e);
            return false; // 出错时允许创建
        }
    }
}
