package com.ybda.service;

import com.ybda.mapper.PendingAssetCheckMapper;
import com.ybda.mapper.TrafficAssetMapper;
import com.ybda.mapper.AlertRecordMapper;
import com.ybda.service.SignGroundMatchingService;
import com.ybda.service.AssetIntegrityService;
import com.ybda.service.AssetInspectionService;
import com.ybda.model.entity.PendingAssetCheck;
import com.ybda.model.entity.TrafficAsset;
import com.ybda.model.entity.AlertRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 定时任务服务
 * 负责GPS轨迹记录、状态检查、超时检测等定时任务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ScheduledTaskService {
    
    private final GpsTrackService gpsTrackService;
    private final PendingAssetCheckMapper pendingAssetCheckMapper;
    private final TrafficAssetMapper trafficAssetMapper;
    private final AlertRecordMapper alertRecordMapper;
    private final SignGroundMatchingService signGroundMatchingService;
    private final AssetIntegrityService assetIntegrityService;
    private final AssetInspectionService assetInspectionService;
    
    // 配置参数
    private static final int PENDING_TIMEOUT_MINUTES = 2; // 2分钟超时
    
    /**
     * GPS位置处理任务
     * 每秒执行一次，直接从Redis获取GPS数据并执行资产匹配检测
     * 不再需要存储GPS到数据库
     */
    @Scheduled(fixedRate = 1000)
    public void processGpsLocationTask() {
        try {
//            log.info("⏰ 执行GPS位置处理任务");
            gpsTrackService.recordGpsTrack(); // 这个方法现在直接处理GPS位置
        } catch (Exception e) {
            log.error("❌ GPS位置处理任务失败", e);
        }
    }
    
    /**
     * 超时检测任务
     * 每10秒执行一次，检查超时的待确认记录并触发报警
     */
//    @Scheduled(fixedRate = 10000)
    @Transactional
    public void timeoutDetectionTask() {
        try {
            log.info("⏰ 执行超时检测任务");

            // 查找超时的待确认记录
            LocalDateTime timeoutBefore = LocalDateTime.now().minusMinutes(PENDING_TIMEOUT_MINUTES);
            List<PendingAssetCheck> timeoutChecks = pendingAssetCheckMapper.findTimeoutPendingChecks(timeoutBefore);

            if (timeoutChecks.isEmpty()) {
                log.info("✅ 没有超时的待确认记录");
                return;
            }

            log.info("⚠️ 发现 {} 条超时的待确认记录", timeoutChecks.size());
            
            List<Long> missingCheckIds = new ArrayList<>();
            List<AlertRecord> alertRecords = new ArrayList<>();
            
            for (PendingAssetCheck check : timeoutChecks) {
                try {
                    // 标记为缺失
                    missingCheckIds.add(check.getId());
                    
                    // 更新资产状态为缺失
                    trafficAssetMapper.updateStatus(check.getExpectedAssetId(),
                            "缺失");
                    
                    // 创建报警记录
                    AlertRecord alertRecord = createMissingAssetAlert(check);
                    alertRecords.add(alertRecord);
                    
                    // 输出详细报警信息到控制台
                    printMissingAssetAlert(check);
                    
                } catch (Exception e) {
                    log.error("处理超时记录失败: checkId={}", check.getId(), e);
                }
            }
            
            // 批量更新待确认记录状态
            if (!missingCheckIds.isEmpty()) {
                pendingAssetCheckMapper.batchMarkAsMissing(missingCheckIds, LocalDateTime.now());
            }
            
            // 批量保存报警记录
            for (AlertRecord alert : alertRecords) {
                alertRecordMapper.insert(alert);
            }
            
            log.info("超时检测完成: 处理 {} 条缺失资产", missingCheckIds.size());
            
        } catch (Exception e) {
            log.error("超时检测任务失败", e);
        }
    }
    
    /**
     * 创建资产缺失报警记录
     */
    private AlertRecord createMissingAssetAlert(PendingAssetCheck check) {
        AlertRecord alert = new AlertRecord();
        alert.setAlertType("资产缺失");
        alert.setAssetId(check.getExpectedAssetId());
        alert.setAlertMessage(String.format("资产缺失报警：%s", check.getAssetName()));
        alert.setAlertTime(LocalDateTime.now());
        alert.setStatus("活跃");
        alert.setCreatedTime(LocalDateTime.now());
        
        // 创建详细报警信息
        Map<String, Object> alertDetails = new HashMap<>();
        alertDetails.put("asset_type", check.getAssetType());
        alertDetails.put("asset_name", check.getAssetName());
        alertDetails.put("expected_asset_id", check.getExpectedAssetId());
        alertDetails.put("gps_location", Map.of(
            "lat", check.getGpsLatitude(),
            "lng", check.getGpsLongitude()
        ));
        alertDetails.put("passed_time", check.getPassedTime().toString());
        alertDetails.put("timeout_minutes", PENDING_TIMEOUT_MINUTES);
        
        try {
            alert.setAlertDetails(new com.fasterxml.jackson.databind.ObjectMapper()
                .writeValueAsString(alertDetails));
        } catch (Exception e) {
            log.warn("序列化报警详情失败", e);
        }
        
        return alert;
    }
    
    /**
     * 输出详细的资产缺失报警信息到控制台
     */
    private void printMissingAssetAlert(PendingAssetCheck check) {
        try {
            // 获取资产详细信息
            TrafficAsset asset = trafficAssetMapper.selectByAssetId(check.getExpectedAssetId());
            
            StringBuilder alertMessage = new StringBuilder();
            alertMessage.append("\n🚨 资产缺失报警 🚨\n");
            alertMessage.append("==========================================\n");
            alertMessage.append("报警时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
            alertMessage.append("资产ID: ").append(check.getExpectedAssetId()).append("\n");
            alertMessage.append("资产类型: ").append(getAssetTypeDescription(check.getAssetType())).append("\n");
            alertMessage.append("资产名称: ").append(check.getAssetName()).append("\n");
            alertMessage.append("资产位置: 纬度 ").append(check.getGpsLatitude()).append(", 经度 ").append(check.getGpsLongitude()).append("\n");
            alertMessage.append("经过时间: ").append(check.getPassedTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
            alertMessage.append("超时时长: ").append(PENDING_TIMEOUT_MINUTES).append(" 分钟\n");
            
            if (asset != null) {
                alertMessage.append("首次检测时间: ").append(
                    asset.getFirstDetectedTime() != null ? 
                    asset.getFirstDetectedTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "未知"
                ).append("\n");
                alertMessage.append("最后检测时间: ").append(
                    asset.getLastDetectedTime() != null ? 
                    asset.getLastDetectedTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "未知"
                ).append("\n");
                alertMessage.append("历史检测次数: ").append(asset.getDetectionCount() != null ? asset.getDetectionCount() : 0).append("\n");
            }
            
            alertMessage.append("==========================================");
            
            // 输出到控制台
            System.out.println(alertMessage.toString());
            log.warn(alertMessage.toString());
            
        } catch (Exception e) {
            log.error("输出报警信息失败", e);
        }
    }
    
    /**
     * 获取资产类型描述
     */
    private String getAssetTypeDescription(String type) {
        return switch (type) {
            case "ground_marking" -> "地面标线";
            case "overhead_sign" -> "空中标牌";
            case "traffic_light" -> "交通信号灯";
            case "barrier" -> "护栏/障碍物";
            default -> type;
        };
    }
    
//    /**
//     * 数据清理任务
//     * 每天凌晨2点执行，清理过期数据
//     */
//    @Scheduled(cron = "0 0 2 * * ?")
//    @Transactional
//    public void dataCleanupTask() {
//        try {
//            log.info("开始执行数据清理任务");
//            
//            // 清理过期待确认记录
//            LocalDateTime beforeTime = LocalDateTime.now().minusDays(CLEANUP_DAYS);
//            int deletedChecks = pendingAssetCheckMapper.deleteBeforeTime(beforeTime);
//            log.info("清理过期待确认记录: {} 条", deletedChecks);
//            
//            log.info("数据清理任务完成");
//            
//        } catch (Exception e) {
//            log.error("数据清理任务失败", e);
//        }
//    }
    
    /**
     * 系统健康检查任务
     * 每5分钟执行一次
     */
//    @Scheduled(fixedRate = 300000)
//    public void systemHealthCheckTask() {
//        try {
//            boolean gpsHealthy = gpsTrackService.checkGpsServiceHealth();
//            
//            if (!gpsHealthy) {
//                log.warn("GPS服务健康检查失败：最近5分钟无GPS数据");
//                
//                // 创建GPS异常报警
//                AlertRecord gpsAlert = new AlertRecord();
//                gpsAlert.setAlertType(AlertRecord.AlertType.GPS_ABNORMAL.getCode());
//                gpsAlert.setAlertMessage("GPS数据异常：连续5分钟未收到有效GPS数据");
//                gpsAlert.setAlertTime(LocalDateTime.now());
//                gpsAlert.setStatus(AlertRecord.AlertStatus.ACTIVE.getCode());
//                gpsAlert.setCreatedTime(LocalDateTime.now());
//                
//                // 检查是否已有相似的活跃报警（避免重复报警）
//                int similarAlerts = alertRecordMapper.countSimilarActiveAlerts(
//                    AlertRecord.AlertType.GPS_ABNORMAL.getCode(), null, 1);
//                
//                if (similarAlerts == 0) {
//                    alertRecordMapper.insert(gpsAlert);
//                    log.warn("创建GPS异常报警");
//                }
//            }
//            
//        } catch (Exception e) {
//            log.error("系统健康检查任务失败", e);
//        }
//    }

    /**
     * 标牌地面对应关系验证超时检查任务
     * 每30秒执行一次，检查超时的标牌验证任务
     */
    @Scheduled(fixedRate = 30000)
    public void signGroundVerificationTimeoutTask() {
        try {
            log.trace("⏰ 执行标牌地面验证超时检查任务");
            signGroundMatchingService.checkTimeoutSignVerifications();
        } catch (Exception e) {
            log.error("❌ 标牌地面验证超时检查任务失败", e);
        }
    }

    /**
     * 资产完整性验证超时检查任务
     * 每60秒执行一次，检查超时的完整性验证任务
     */
    @Scheduled(fixedRate = 60000)
    public void assetIntegrityTimeoutTask() {
        try {
            log.trace("⏰ 执行资产完整性验证超时检查任务");
            assetIntegrityService.checkTimeoutTasks();
        } catch (Exception e) {
            log.error("❌ 资产完整性验证超时检查任务失败", e);
        }
    }

    /**
     * 资产状态更新任务
     * 每5分钟执行一次，基于巡检历史更新资产状态
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void assetStatusUpdateTask() {
        try {
            log.trace("⏰ 执行资产状态更新任务");

            // 获取所有资产并更新状态
            List<TrafficAsset> allAssets = trafficAssetMapper.selectList(null);
            int updatedCount = 0;

            for (TrafficAsset asset : allAssets) {
                try {
                    assetInspectionService.updateAssetStatusBasedOnInspections(asset.getAssetId());
                    updatedCount++;
                } catch (Exception e) {
                    log.warn("⚠️ 更新资产状态失败: assetId={}", asset.getAssetId(), e);
                }
            }

            log.debug("✅ 资产状态更新完成: 共处理{}个资产", updatedCount);

        } catch (Exception e) {
            log.error("❌ 资产状态更新任务失败", e);
        }
    }

    /**
     * 巡检记录清理任务
     * 每天凌晨2点执行，清理旧的巡检记录
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void inspectionRecordCleanupTask() {
        try {
            log.info("⏰ 执行巡检记录清理任务");

            // 每个资产保留最新50条巡检记录
            assetInspectionService.cleanupOldInspectionRecords(50);

            log.info("✅ 巡检记录清理任务完成");

        } catch (Exception e) {
            log.error("❌ 巡检记录清理任务失败", e);
        }
    }
}
