package com.ybda.service;

import com.ybda.model.entity.DetectionRecord;
import com.ybda.utils.SignParsingUtils;

import java.util.List;

/**
 * 标牌地面对应关系验证服务接口
 */
public interface SignGroundMatchingService {
    
    /**
     * 处理空中标牌检测，启动地面标线等待验证
     * @param detectionRecord 检测记录
     * @param signName 标牌名称
     */
    void processOverheadSignDetection(DetectionRecord detectionRecord, String signName);
    
    /**
     * 处理地面标线检测，验证是否与空中标牌对应
     * @param detectionRecord 检测记录
     * @param markingName 标线名称
     */
    void processGroundMarkingDetection(DetectionRecord detectionRecord, String markingName);
    
    /**
     * 检查超时的标牌验证任务
     */
    void checkTimeoutSignVerifications();

}
