package com.ybda.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ybda.model.dto.CoordinatePoint;
import com.ybda.model.entity.TrafficAsset;
import com.ybda.model.enums.GeometryType;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 几何计算工具类
 */
@Slf4j
public class GeometryUtils {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 判断GPS点是否在资产范围内
     * @param gpsPoint GPS坐标点
     * @param asset 资产
     * @param maxDistance 最大距离（米）
     * @return 是否在范围内
     */
    public static boolean isPointNearAsset(CoordinatePoint gpsPoint, TrafficAsset asset, double maxDistance) {
        if (gpsPoint == null || asset == null) {
            return false;
        }
        
        try {
            GeometryType geometryType = asset.getGeometryTypeEnum();

            switch (geometryType) {
                case POINT -> {
                    return isPointNearPoint(gpsPoint, asset, maxDistance);
                }
                case LINE -> {
                    return isPointNearLine(gpsPoint, asset, maxDistance);
                }
                case POLYGON -> {
                    return isPointInPolygon(gpsPoint, asset, maxDistance);
                }
                default -> {
                    log.warn("未知的几何类型: {}", asset.getGeometryType());
                    return false;
                }
            }
        } catch (Exception e) {
            log.error("判断GPS点是否在资产范围内失败: assetId={}", asset.getAssetId(), e);
            return false;
        }
    }
    
    /**
     * 判断GPS点是否接近点状资产
     */
    private static boolean isPointNearPoint(CoordinatePoint gpsPoint, TrafficAsset asset, double maxDistance) {
        if (asset.getLatitude() == null || asset.getLongitude() == null) {
            return false;
        }
        
        CoordinatePoint assetPoint = new CoordinatePoint(asset.getLatitude(), asset.getLongitude());
        double distance = gpsPoint.distanceTo(assetPoint);
        
        log.debug("点状资产距离计算: GPS({}, {}) -> 资产({}, {}) = {}米", 
            gpsPoint.getLat(), gpsPoint.getLng(),
            asset.getLatitude(), asset.getLongitude(), distance);
        
        return distance <= maxDistance;
    }
    
    /**
     * 判断GPS点是否接近线状资产
     */
    private static boolean isPointNearLine(CoordinatePoint gpsPoint, TrafficAsset asset, double maxDistance) {
        try {
            if (asset.getGeometryCoordinates() == null) {
                log.warn("线状资产缺少坐标数据: assetId={}", asset.getAssetId());
                return false;
            }
            
            // 解析线状坐标
            List<CoordinatePoint> linePoints = objectMapper.readValue(
                asset.getGeometryCoordinates(), 
                new TypeReference<List<CoordinatePoint>>() {}
            );
            
            if (linePoints.size() < 2) {
                log.warn("线状资产坐标点不足: assetId={}, pointCount={}", 
                    asset.getAssetId(), linePoints.size());
                return false;
            }
            
            // 计算GPS点到线段的最短距离
            double minDistance = Double.MAX_VALUE;
            
            for (int i = 0; i < linePoints.size() - 1; i++) {
                CoordinatePoint p1 = linePoints.get(i);
                CoordinatePoint p2 = linePoints.get(i + 1);
                
                double distance = distanceFromPointToLineSegment(gpsPoint, p1, p2);
                minDistance = Math.min(minDistance, distance);
                
                // 如果已经在范围内，直接返回
                if (distance <= maxDistance) {
                    log.debug("GPS点在线状资产范围内: 距离={}米, 线段=({},{}) -> ({},{})", 
                        distance, p1.getLat(), p1.getLng(), p2.getLat(), p2.getLng());
                    return true;
                }
            }
            
            log.debug("GPS点不在线状资产范围内: 最短距离={}米, 要求距离<={}米", minDistance, maxDistance);
            return false;
            
        } catch (Exception e) {
            log.error("解析线状资产坐标失败: assetId={}", asset.getAssetId(), e);
            return false;
        }
    }
    
    /**
     * 判断GPS点是否在面状资产内
     */
    private static boolean isPointInPolygon(CoordinatePoint gpsPoint, TrafficAsset asset, double maxDistance) {
        try {
            if (asset.getGeometryCoordinates() == null) {
                log.warn("面状资产缺少坐标数据: assetId={}", asset.getAssetId());
                return false;
            }
            
            // 解析多边形坐标
            List<CoordinatePoint> polygonPoints = objectMapper.readValue(
                asset.getGeometryCoordinates(), 
                new TypeReference<List<CoordinatePoint>>() {}
            );
            
            if (polygonPoints.size() < 3) {
                log.warn("面状资产坐标点不足: assetId={}, pointCount={}", 
                    asset.getAssetId(), polygonPoints.size());
                return false;
            }
            
            // 使用射线法判断点是否在多边形内
            boolean inside = isPointInPolygonRayCasting(gpsPoint, polygonPoints);
            
            if (inside) {
                log.debug("GPS点在面状资产内部");
                return true;
            }
            
            // 如果不在内部，检查是否在边界附近
            double minDistance = Double.MAX_VALUE;
            for (int i = 0; i < polygonPoints.size(); i++) {
                CoordinatePoint p1 = polygonPoints.get(i);
                CoordinatePoint p2 = polygonPoints.get((i + 1) % polygonPoints.size());
                
                double distance = distanceFromPointToLineSegment(gpsPoint, p1, p2);
                minDistance = Math.min(minDistance, distance);
            }
            
            boolean nearBoundary = minDistance <= maxDistance;
            log.debug("GPS点{}面状资产边界: 最短距离={}米", nearBoundary ? "接近" : "远离", minDistance);
            
            return nearBoundary;
            
        } catch (Exception e) {
            log.error("解析面状资产坐标失败: assetId={}", asset.getAssetId(), e);
            return false;
        }
    }
    
    /**
     * 计算点到线段的最短距离
     */
    private static double distanceFromPointToLineSegment(CoordinatePoint point, CoordinatePoint lineStart, CoordinatePoint lineEnd) {
        // 将地理坐标转换为平面坐标进行计算（简化处理）
        double x = point.getLng();
        double y = point.getLat();
        double x1 = lineStart.getLng();
        double y1 = lineStart.getLat();
        double x2 = lineEnd.getLng();
        double y2 = lineEnd.getLat();
        
        double A = x - x1;
        double B = y - y1;
        double C = x2 - x1;
        double D = y2 - y1;
        
        double dot = A * C + B * D;
        double lenSq = C * C + D * D;
        
        if (lenSq == 0) {
            // 线段退化为点
            return point.distanceTo(lineStart);
        }
        
        double param = dot / lenSq;
        
        CoordinatePoint closestPoint;
        if (param < 0) {
            closestPoint = lineStart;
        } else if (param > 1) {
            closestPoint = lineEnd;
        } else {
            closestPoint = new CoordinatePoint(
                y1 + param * D,
                x1 + param * C
            );
        }
        
        return point.distanceTo(closestPoint);
    }
    
    /**
     * 使用射线法判断点是否在多边形内
     */
    private static boolean isPointInPolygonRayCasting(CoordinatePoint point, List<CoordinatePoint> polygon) {
        double x = point.getLng();
        double y = point.getLat();
        
        boolean inside = false;
        int j = polygon.size() - 1;
        
        for (int i = 0; i < polygon.size(); i++) {
            double xi = polygon.get(i).getLng();
            double yi = polygon.get(i).getLat();
            double xj = polygon.get(j).getLng();
            double yj = polygon.get(j).getLat();
            
            if (((yi > y) != (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
                inside = !inside;
            }
            j = i;
        }
        
        return inside;
    }
}
