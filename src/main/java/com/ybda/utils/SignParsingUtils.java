package com.ybda.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 标牌解析工具类
 * 用于解析空中标牌的车道指示信息
 */
@Slf4j
public class SignParsingUtils {
    
    /**
     * 车道方向枚举
     */
    public enum LaneDirection {
        LEFT("左转", "左", "L"),
        STRAIGHT("直行", "直", "S"), 
        RIGHT("右转", "右", "R"),
        LEFT_STRAIGHT("左转直行", "左直", "LS"),
        STRAIGHT_RIGHT("直行右转", "直右", "SR"),
        U_TURN("掉头", "U");
        
        private final String[] keywords;
        
        LaneDirection(String... keywords) {
            this.keywords = keywords;
        }
        
        public String[] getKeywords() {
            return keywords;
        }
    }
    
    /**
     * 车道信息
     */
    public static class LaneInfo {
        private int laneNumber;
        private LaneDirection direction;
        private String originalText;

        // 无参构造函数（JSON反序列化需要）
        public LaneInfo() {}

        public LaneInfo(int laneNumber, LaneDirection direction, String originalText) {
            this.laneNumber = laneNumber;
            this.direction = direction;
            this.originalText = originalText;
        }

        // Getters and Setters
        public int getLaneNumber() { return laneNumber; }
        public void setLaneNumber(int laneNumber) { this.laneNumber = laneNumber; }

        public LaneDirection getDirection() { return direction; }
        public void setDirection(LaneDirection direction) { this.direction = direction; }

        public String getOriginalText() { return originalText; }
        public void setOriginalText(String originalText) { this.originalText = originalText; }

        @Override
        public String toString() {
            return String.format("车道%d:%s", laneNumber, direction != null ? direction.keywords[0] : "未知");
        }
    }
    
    /**
     * 解析空中标牌的车道指示信息
     * 例如: "1左2直3右" → [车道1:左转, 车道2:直行, 车道3:右转]
     */
    public static List<LaneInfo> parseOverheadSign(String signName) {
        List<LaneInfo> laneInfos = new ArrayList<>();
        
        if (signName == null || signName.trim().isEmpty()) {
            return laneInfos;
        }
        
        try {
            // 标准化文本
            String normalizedText = normalizeSignText(signName);
            log.debug("🔍 标牌文本标准化: '{}' → '{}'", signName, normalizedText);
            
            // 模式1: "1左2直3右" 格式
            Pattern pattern1 = Pattern.compile("(\\d+)([左直右掉])");
            Matcher matcher1 = pattern1.matcher(normalizedText);
            
            while (matcher1.find()) {
                int laneNumber = Integer.parseInt(matcher1.group(1));
                String directionText = matcher1.group(2);
                LaneDirection direction = parseDirection(directionText);
                
                if (direction != null) {
                    laneInfos.add(new LaneInfo(laneNumber, direction, matcher1.group()));
                    log.debug("  解析到车道: 车道{} → {}", laneNumber, direction.keywords[0]);
                }
            }
            
            // 模式2: "第1车道左转，第2车道直行" 格式
            if (laneInfos.isEmpty()) {
                Pattern pattern2 = Pattern.compile("第?(\\d+)车道([左直右掉][转行]?)");
                Matcher matcher2 = pattern2.matcher(normalizedText);
                
                while (matcher2.find()) {
                    int laneNumber = Integer.parseInt(matcher2.group(1));
                    String directionText = matcher2.group(2);
                    LaneDirection direction = parseDirection(directionText);
                    
                    if (direction != null) {
                        laneInfos.add(new LaneInfo(laneNumber, direction, matcher2.group()));
                        log.debug("  解析到车道: 车道{} → {}", laneNumber, direction.keywords[0]);
                    }
                }
            }
            
            // 模式3: "左转直行右转" 格式（按顺序分配车道号）
            if (laneInfos.isEmpty()) {
                List<LaneDirection> directions = parseDirectionsOnly(normalizedText);
                for (int i = 0; i < directions.size(); i++) {
                    laneInfos.add(new LaneInfo(i + 1, directions.get(i), directions.get(i).keywords[0]));
                    log.debug("  推断车道: 车道{} → {}", i + 1, directions.get(i).keywords[0]);
                }
            }
            
            log.info("🏷️ 标牌解析完成: '{}' → {} 个车道信息", signName, laneInfos.size());
            
        } catch (Exception e) {
            log.error("❌ 标牌解析失败: signName={}", signName, e);
        }
        
        return laneInfos;
    }
    
    /**
     * 标准化标牌文本
     */
    private static String normalizeSignText(String text) {
        return text.replace("转", "")
                  .replace("行", "")
                  .replace("道", "")
                  .replace("车", "")
                  .replace("第", "")
                  .replace("，", "")
                  .replace(",", "")
                  .replace(" ", "")
                  .replace("向", "");
    }
    
    /**
     * 解析方向文本
     */
    private static LaneDirection parseDirection(String directionText) {
        for (LaneDirection direction : LaneDirection.values()) {
            for (String keyword : direction.getKeywords()) {
                if (directionText.contains(keyword) || keyword.contains(directionText)) {
                    return direction;
                }
            }
        }
        return null;
    }
    
    /**
     * 仅解析方向（不包含车道号）
     */
    private static List<LaneDirection> parseDirectionsOnly(String text) {
        List<LaneDirection> directions = new ArrayList<>();
        
        // 按优先级检查方向关键词
        if (text.contains("左")) directions.add(LaneDirection.LEFT);
        if (text.contains("直")) directions.add(LaneDirection.STRAIGHT);
        if (text.contains("右")) directions.add(LaneDirection.RIGHT);
        if (text.contains("掉")) directions.add(LaneDirection.U_TURN);
        
        return directions;
    }
    
    /**
     * 检查地面标线是否与空中标牌匹配
     */
    public static boolean isGroundMarkingMatchingSign(List<LaneInfo> signLanes, String groundMarkingName) {
        if (signLanes.isEmpty() || groundMarkingName == null) {
            return false;
        }
        
        // 解析地面标线的方向
        LaneDirection groundDirection = parseGroundMarkingDirection(groundMarkingName);
        if (groundDirection == null) {
            return false;
        }
        
        // 检查是否有匹配的车道方向
        boolean matches = signLanes.stream()
            .anyMatch(lane -> lane.getDirection() == groundDirection);
        
        log.debug("🔍 地面标线匹配检查: '{}' ({}) vs 标牌车道 → {}", 
            groundMarkingName, groundDirection.keywords[0], matches ? "匹配" : "不匹配");
        
        return matches;
    }
    
    /**
     * 解析地面标线的方向
     */
    private static LaneDirection parseGroundMarkingDirection(String markingName) {
        String normalizedName = markingName.toLowerCase();
        
        if (normalizedName.contains("左转") || normalizedName.contains("左") || normalizedName.contains("left")) {
            return LaneDirection.LEFT;
        }
        if (normalizedName.contains("直行") || normalizedName.contains("直") || normalizedName.contains("straight")) {
            return LaneDirection.STRAIGHT;
        }
        if (normalizedName.contains("右转") || normalizedName.contains("右") || normalizedName.contains("right")) {
            return LaneDirection.RIGHT;
        }
        if (normalizedName.contains("掉头") || normalizedName.contains("u")) {
            return LaneDirection.U_TURN;
        }
        
        return null;
    }
    
    /**
     * 生成期望的地面标线列表
     */
    public static List<String> generateExpectedGroundMarkings(List<LaneInfo> signLanes) {
        List<String> expectedMarkings = new ArrayList<>();

        for (LaneInfo lane : signLanes) {
            String expectedMarking = String.format("车道%d%s箭头", lane.getLaneNumber(), lane.getDirection().keywords[0]);
            expectedMarkings.add(expectedMarking);
        }

        return expectedMarkings;
    }

    /**
     * 根据标牌类型生成期望的地面标识（扩展版本）
     */
    public static List<String> generateExpectedGroundMarkingsBySignType(String signName) {
        List<String> expectedMarkings = new ArrayList<>();

        if (signName == null || signName.trim().isEmpty()) {
            return expectedMarkings;
        }

        String lowerSignName = signName.toLowerCase();

        // 车道指示标牌
        List<LaneInfo> laneInfos = parseOverheadSign(signName);
        if (!laneInfos.isEmpty()) {
            return generateExpectedGroundMarkings(laneInfos);
        }

        // 限速标牌
        if (lowerSignName.contains("限速") || lowerSignName.contains("speed")) {
            expectedMarkings.add("限速标线");
            expectedMarkings.add("数字标识");
            return expectedMarkings;
        }

        // 停车相关标牌
        if (lowerSignName.contains("停车") || lowerSignName.contains("parking")) {
            expectedMarkings.add("停车线");
            expectedMarkings.add("停车位标线");
            return expectedMarkings;
        }

        // 禁止标牌
        if (lowerSignName.contains("禁止") || lowerSignName.contains("禁")) {
            expectedMarkings.add("禁止标线");
            expectedMarkings.add("黄色实线");
            return expectedMarkings;
        }

        // 学校区域标牌
        if (lowerSignName.contains("学校") || lowerSignName.contains("school")) {
            expectedMarkings.add("学校标识");
            expectedMarkings.add("减速带");
            expectedMarkings.add("斑马线");
            return expectedMarkings;
        }

        // 施工标牌
        if (lowerSignName.contains("施工") || lowerSignName.contains("construction")) {
            expectedMarkings.add("施工标线");
            expectedMarkings.add("临时标识");
            return expectedMarkings;
        }

        // 默认情况：通用地面标识
        expectedMarkings.add("相关地面标识");
        return expectedMarkings;
    }
}
