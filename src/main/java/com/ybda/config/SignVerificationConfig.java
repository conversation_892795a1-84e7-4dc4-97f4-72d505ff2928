package com.ybda.config;

import com.ybda.utils.SignParsingUtils;

import java.util.List;
import java.util.Map;

/**
 * 标牌验证配置类
 * 定义各种标牌类型及其对应的地面标识验证规则
 */
public class SignVerificationConfig {
    
    /**
     * 标牌验证规则配置
     */
    public static final Map<String, SignVerificationRule> VERIFICATION_RULES = Map.of(
        
        // 车道指示类标牌
        "LANE_DIRECTION", new SignVerificationRule(
            List.of("左", "直", "右", "掉头", "车道"),
            List.of("箭头", "导向线", "车道线"),
            "车道指示标牌需要对应的地面导向箭头"
        ),
        
        // 限速类标牌
        "SPEED_LIMIT", new SignVerificationRule(
            List.of("限速", "speed", "km/h", "公里"),
            List.of("限速标线", "数字标识", "减速带"),
            "限速标牌需要对应的地面限速标识"
        ),
        
        // 停车类标牌
        "PARKING", new SignVerificationRule(
            List.of("停车", "parking", "车位", "泊车"),
            List.of("停车线", "停车位标线", "P标识"),
            "停车标牌需要对应的地面停车标线"
        ),
        
        // 禁止类标牌
        "PROHIBITION", new SignVerificationRule(
            List.of("禁止", "禁", "不准", "严禁"),
            List.of("禁止标线", "黄色实线", "禁停标识"),
            "禁止类标牌需要对应的地面禁止标识"
        ),
        
        // 学校区域标牌
        "SCHOOL_ZONE", new SignVerificationRule(
            List.of("学校", "school", "校园", "学生"),
            List.of("学校标识", "减速带", "斑马线", "慢行标线"),
            "学校区域标牌需要对应的地面安全设施"
        ),
        
        // 施工类标牌
        "CONSTRUCTION", new SignVerificationRule(
            List.of("施工", "construction", "维修", "作业"),
            List.of("施工标线", "临时标识", "导流线"),
            "施工标牌需要对应的临时地面标识"
        ),
        
        // 指路类标牌
        "DIRECTION", new SignVerificationRule(
            List.of("出口", "入口", "方向", "前往"),
            List.of("导向箭头", "分流线", "指示标线"),
            "指路标牌需要对应的地面导向标识"
        )
    );
    
    /**
     * 标牌验证规则
     */
    public static class SignVerificationRule {
        private final List<String> signKeywords;        // 标牌关键词
        private final List<String> expectedMarkings;    // 期望的地面标识
        private final String description;               // 规则描述
        
        public SignVerificationRule(List<String> signKeywords, List<String> expectedMarkings, String description) {
            this.signKeywords = signKeywords;
            this.expectedMarkings = expectedMarkings;
            this.description = description;
        }
        
        public List<String> getSignKeywords() { return signKeywords; }
        public List<String> getExpectedMarkings() { return expectedMarkings; }
        public String getDescription() { return description; }
        
        /**
         * 检查标牌名称是否匹配此规则
         */
        public boolean matchesSign(String signName) {
            if (signName == null) return false;
            String lowerSignName = signName.toLowerCase();
            return signKeywords.stream().anyMatch(lowerSignName::contains);
        }
        
        /**
         * 检查地面标识是否匹配此规则
         */
        public boolean matchesGroundMarking(String markingName) {
            if (markingName == null) return false;
            String lowerMarkingName = markingName.toLowerCase();
            return expectedMarkings.stream().anyMatch(expected -> 
                lowerMarkingName.contains(expected.toLowerCase()) || 
                expected.toLowerCase().contains(lowerMarkingName)
            );
        }
    }
    
    /**
     * 根据标牌名称获取适用的验证规则
     */
    public static SignVerificationRule getVerificationRule(String signName) {
        for (SignVerificationRule rule : VERIFICATION_RULES.values()) {
            if (rule.matchesSign(signName)) {
                return rule;
            }
        }
        return null; // 没有匹配的规则
    }
    
    /**
     * 根据标牌名称生成期望的地面标识列表
     */
    public static List<String> getExpectedGroundMarkings(String signName) {
        SignVerificationRule rule = getVerificationRule(signName);
        if (rule != null) {
            return rule.getExpectedMarkings();
        }
        
        // 如果没有匹配的规则，尝试解析车道指示
        List<SignParsingUtils.LaneInfo> laneInfos = SignParsingUtils.parseOverheadSign(signName);
        if (!laneInfos.isEmpty()) {
            return SignParsingUtils.generateExpectedGroundMarkings(laneInfos);
        }
        
        // 默认返回通用标识
        return List.of("相关地面标识");
    }
    
    /**
     * 检查地面标识是否与标牌匹配
     */
    public static boolean isGroundMarkingMatchingSign(String signName, String markingName) {
        // 首先尝试车道指示匹配
        List<SignParsingUtils.LaneInfo> laneInfos = SignParsingUtils.parseOverheadSign(signName);
        if (!laneInfos.isEmpty()) {
            return SignParsingUtils.isGroundMarkingMatchingSign(laneInfos, markingName);
        }
        
        // 然后尝试规则匹配
        SignVerificationRule rule = getVerificationRule(signName);
        if (rule != null) {
            return rule.matchesGroundMarking(markingName);
        }
        
        // 默认不匹配
        return false;
    }
}
