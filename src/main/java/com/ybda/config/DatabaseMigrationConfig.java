package com.ybda.config;

import com.ybda.util.DatabaseMigrationUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

/**
 * 数据库迁移配置
 * 在应用启动时自动检查并修复数据库结构
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class DatabaseMigrationConfig {

    private final DatabaseMigrationUtil migrationUtil;

    /**
     * 启动时自动修复数据库结构
     */
    @Bean
    @Order(1) // 确保在其他初始化之前执行
    public CommandLineRunner databaseMigrationRunner() {
        return args -> {
            try {
                log.info("🚀 开始数据库结构检查和修复...");
                
                // 检查表结构
                if (!migrationUtil.checkTableStructure()) {
                    log.info("🔧 检测到表结构需要更新，开始自动修复...");
                    migrationUtil.fixInspectionRecordsConstraints();
                } else {
                    log.info("✅ 数据库结构检查通过，无需修复");
                }
                
            } catch (Exception e) {
                log.error("❌ 数据库结构检查/修复失败", e);
                // 不抛出异常，避免影响应用启动
                log.warn("⚠️ 应用将继续启动，但可能存在数据库结构问题");
            }
        };
    }
}
